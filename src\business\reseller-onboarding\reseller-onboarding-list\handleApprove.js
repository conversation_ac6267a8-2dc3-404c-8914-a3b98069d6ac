import {
  STATUS_MAP,
  SUPER_ADMIN_GROUP_ID,
  RESELLER_ONBOARD_GROUP_ID,
  SUPER_ADMIN_ROLE_ID,
  RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID,
  RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID,
  RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID,
  APPROVE_ACCESS,
  enableReviewAndApproveStatus,


} from "@/business/reseller-onboarding/reseller-onboarding-list/constants.js";

import {
  hasActionPermission,
} from "@/business/reseller-onboarding/reseller-onboarding-list/utils.js";



export const enableShowReviewAndApprove = (roleAccessList) => {
  return hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, APPROVE_ACCESS) ||
    hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID, APPROVE_ACCESS) ||
    hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID, APPROVE_ACCESS) ||
    hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID, APPROVE_ACCESS);
};

export const enableReviewAndApprove = (roleAccessList, statusCode) => {
  const superAdminFlag = hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, APPROVE_ACCESS);
  if (superAdminFlag && enableReviewAndApproveStatus.includes(statusCode)) {
    return true;
  }
  const resellerOpsFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID, APPROVE_ACCESS);
  if (resellerOpsFlag && STATUS_MAP.ENQUIRY_SUBMITTED === statusCode) {
    return true;
  }
  const countryManagerFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID, APPROVE_ACCESS);
  if (countryManagerFlag && STATUS_MAP.ENQUIRY_SUBMITTED === statusCode) {
    return true;
  }
  const complianceFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID, APPROVE_ACCESS);
  if (complianceFlag && STATUS_MAP.PENDING_KYB === statusCode) {
    return true;
  }
  return false;
};