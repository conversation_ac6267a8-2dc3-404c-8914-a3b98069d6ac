import { ref } from 'vue'

/**
 * 低代码平台API封装
 * 根据您提供的API文档整合所有接口
 * 使用项目已有的$api（基于ofetch）
 */
export function useLowCodeAPI() {
  const loading = ref(false)
  const error = ref(null)

  // ===== 工具方法 =====
  
  /**
   * 通用API请求方法
   */
  async function apiRequest(url, options = {}) {
    loading.value = true
    error.value = null
    
    try {
      // 使用项目已有的$api
      const response = await $api(url, options)
      return response
    } catch (err) {
      error.value = err.data?.message || err.message
      console.error(`API请求失败 [${options.method || 'GET'} ${url}]:`, err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // ===== LowCodePage API =====
  
  /**
   * 创建新页面
   * @param {Object} pageData - 页面数据 { pageName, layout }
   */
  async function createPage(pageData) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page', {
      method: 'POST',
      body: pageData
    })
  }

  /**
   * 根据页面名称查询页面
   * @param {string} scenarioId - 场景ID
   */
  async function queryPage(scenarioId) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page', {
      method: 'GET',
      query: { scenarioId }
    })
  }

  /**
   * 更新页面布局
   * @param {Object} layoutData - 布局数据 { id, layout }
   */
  async function updatePage(layoutData) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page', {
      method: 'PUT',
      body: layoutData
    })
  }

  /**
   * 更新页面状态
   * @param {Object} statusData - 状态数据 { id, active }
   */
  async function updatePageStatus(statusData) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page/status', {
      method: 'PUT',
      body: statusData
    })
  }

  /**
   * 获取所有活跃页面名称列表
   */
  async function listActivePages() {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page/list', {
      method: 'GET'
    })
  }

  // ===== Metadata API =====
  
  /**
   * 创建新场景
   * @param {Object} scenarioData - 场景数据
   */
  async function createScenario(scenarioData) {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/scenario', {
      method: 'POST',
      body: scenarioData
    })
  }

  /**
   * 获取所有场景列表
   */
 async function listAllScenarios() {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/scenario/list-all', {
      method: 'GET'
    })
  }

  /**
   * 获取场景列表
   */
  async function listScenarios() {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/scenario/list', {
      method: 'GET'
    })
  }

  /**
   * 添加元数据字段
   * @param {Object} fieldData - 字段数据
   */
  async function addMetaField(fieldData) {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/meta-field', {
      method: 'POST',
      body: fieldData
    })
  }

  /**
   * 创建元数据索引
   * @param {Object} indexData - 索引数据
   */
  async function createMetaIndex(indexData) {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/meta-index', {
      method: 'POST',
      body: indexData
    })
  }

  /**
   * 获取元数据
   * @param {Object} queryData - 查询参数
   */
  async function getMetadata(queryData) {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/scenario', {
      method: 'GET',
      query: queryData
    })
  }

  /**
   * 获取元数据索引
   * @param {Object} queryData - 查询参数
   */
  async function getIndexes(queryData) {
    return await apiRequest('/api/admin-api/v1/config-center/metadata/meta-index/page', {
      method: 'POST',
      body: queryData
    })
  }

  // ===== ConfigItem API =====
  
  /**
   * 创建配置项
   * @param {Object} configData - 配置数据
   */
  async function createConfig(configData) {
    return await apiRequest('/api/admin-api/v1/config-center/items', {
      method: 'POST',
      body: configData
    })
  }

  /**
   * 更新配置项
   * @param {Object} configData - 配置数据
   */
  async function updateConfig(configData) {
    return await apiRequest('/api/admin-api/v1/config-center/items', {
      method: 'PUT',
      body: configData
    })
  }

  /**
   * 更新配置项状态
   * @param {number} configItemId - 配置项ID
   * @param {boolean} active - 激活状态
   */
  async function updateConfigStatus(configItemId, active) {
    return await apiRequest('/v1/admin-api/config-center/items/active', {
      method: 'PUT',
      query: { 
        configItemId, 
        active 
      }
    })
  }

  /**
   * 根据ID获取配置项
   * @param {number} configItemId - 配置项ID
   */
  async function getConfigById(configItemId) {
    return await apiRequest('/api/admin-api/v1/config-center/items/get', {
      method: 'GET',
      query: { configItemId }
    })
  }

  /**
   * 查询配置项列表
   * @param {Object} queryData - 查询参数
   */
  async function queryConfigList(queryData) {
    return await apiRequest('/api/admin-api/v1/config-center/items/page', {
      method: 'POST',
      body: queryData
    })
  }

  /**
   * 根据path获取页面json
   * @param {string} path - 页面路径
   */
  async function getPageJsonByPath(path) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page', {
      method: 'GET',
      query: { path }
    })
  }

  // ===== Plugin API =====
  
  /**
   * 获取选择元数据
   */
  async function getSelectMetadata() {
    return await apiRequest('/api/admin-api/v1/config-center/plugin/select-metadata', {
      method: 'POST'
    })
  }

  /**
   * 获取选择项
   * @param {Object} queryData - 查询参数
   */
  async function getSelectItems(queryData) {
    return await apiRequest('/api/admin-api/v1/config-center/plugin/select-metadata', {
      method: 'POST',
      body: queryData
    })
  }

  /**
   * 获取页面配置
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型 (list, create, edit, detail)
   */
  async function getPageConfig(scenario, pageType = 'list') {
    try {
      // 首先尝试从后端获取页面配置
      const response = await apiRequest(`/api/admin-api/v1/config-center/low-code-page/config`, {
        method: 'GET',
        query: { scenario, pageType }
      })
      
      if (response && response.data) {
        return response.data
      }
      
      // 如果后端没有配置，尝试从本地生成
      console.log('后端无配置，尝试本地生成页面配置')
      return await generateLocalPageConfig(scenario, pageType)
      
    } catch (err) {
      console.error('获取页面配置失败:', err)
      // 尝试从本地生成作为备选方案
      return await generateLocalPageConfig(scenario, pageType)
    }
  }

  /**
   * 本地生成页面配置（备选方案）
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   */
  async function generateLocalPageConfig(scenario, pageType) {
    try {
      // 获取场景元数据
      const metaResponse = await getMetadata({ scenario })
      if (!metaResponse || !metaResponse.data) {
        throw new Error('场景元数据不存在')
      }
      
      const backendMeta = metaResponse.data
      
      // 使用页面生成器生成配置
      const { generatePagesFromBackendMeta } = await import('../editor/pageGenerator.js')
      const pages = generatePagesFromBackendMeta(backendMeta)
      
      return pages[pageType] || null
      
    } catch (err) {
      console.error('本地生成页面配置失败:', err)
      throw new Error(`无法生成页面配置: ${err.message}`)
    }
  }

  /**
   * 保存页面配置到后端
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   * @param {Object} config - 页面配置
   */
  async function savePageConfig(scenario, pageType, config) {
    return await apiRequest('/api/admin-api/v1/config-center/low-code-page/config', {
      method: 'POST',
      body: {
        scenario,
        pageType,
        config: JSON.stringify(config)
      }
    })
  }

  // ===== 高级封装方法 =====
  
  /**
   * 完整的页面管理工作流
   */
  const pageWorkflow = {
    /**
     * 创建完整页面（包含场景和配置）
     */
    async createFullPage(pageData, scenarioData, configData) {
      try {
        // 1. 创建场景
        const scenario = await createScenario(scenarioData)
        
        // 2. 添加字段
        for (const field of scenarioData.fields) {
          await addMetaField({
            scenarioId: scenario.data,
            ...field
          })
        }
        
        // 3. 创建页面
        const page = await createPage(pageData)
        
        // 4. 创建配置
        if (configData) {
          await createConfig({
            scenario: scenarioData.scenario,
            configData
          })
        }
        
        return {
          scenario: scenario.data,
          page: page.data,
          success: true
        }
      } catch (error) {
        console.error('创建完整页面失败:', error)
        throw error
      }
    },

    /**
     * 获取页面完整信息（页面+元数据+配置）
     */
    async getFullPageInfo(pageName) {
      try {
        // 1. 获取页面信息
        const page = await queryPage(pageName)
        
        // 2. 获取元数据
        const metadata = await getSelectMetadata()
        
        // 3. 获取配置信息
        const configs = await queryConfigList({
          scenario: page.data.scenario || pageName
        })
        
        return {
          page: page.data,
          metadata: metadata.data,
          configs: configs.data.records || [],
          success: true
        }
      } catch (error) {
        console.error('获取页面完整信息失败:', error)
        throw error
      }
    }
  }

  /**
   * 元数据管理工作流
   */
  const metadataWorkflow = {
    /**
     * 批量创建字段
     */
    async batchCreateFields(scenarioId, fields) {
      const results = []
      
      for (const field of fields) {
        try {
          const result = await addMetaField({
            scenarioId,
            ...field
          })
          results.push({ success: true, data: result.data, field })
        } catch (error) {
          results.push({ success: false, error, field })
        }
      }
      
      return results
    },

    /**
     * 创建标准CRUD场景
     */
    async createCRUDScenario(scenarioName, displayName, fields) {
      try {
        // 1. 创建场景
        const scenario = await createScenario({
          scenario: scenarioName,
          description: `${displayName}的CRUD场景`,
          fields: fields
        })
        
        // 2. 批量创建字段
        const fieldResults = await this.batchCreateFields(scenario.data, fields)
        
        // 3. 创建索引（如果有唯一字段）
        const uniqueFields = fields.filter(f => f.unique)
        for (const field of uniqueFields) {
          await createMetaIndex({
            scenarioId: scenario.data,
            indexName: `idx_${field.name}`,
            indexFields: field.name,
            unique: true
          })
        }
        
        return {
          scenario: scenario.data,
          fields: fieldResults,
          success: true
        }
      } catch (error) {
        console.error('创建CRUD场景失败:', error)
        throw error
      }
    }
  }

  return {
    // 状态
    loading,
    error,
    
    // 基础API方法
    // LowCodePage API
    createPage,
    queryPage,
    updatePage,
    updatePageStatus,
    listActivePages,
    
    // Metadata API
    createScenario,
    listAllScenarios,
    listScenarios,
    addMetaField,
    createMetaIndex,
    getMetadata,
    getIndexes,
    
    // ConfigItem API
    createConfig,
    updateConfig,
    updateConfigStatus,
    getConfigById,
    queryConfigList,
    
    // Plugin API
    getSelectMetadata,
    getSelectItems,
    
    // 高级工作流
    pageWorkflow,
    metadataWorkflow,
    
    // 工具方法
    apiRequest
  }
} 