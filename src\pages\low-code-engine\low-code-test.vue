<template>
  <div class="low-code-test">
    <VCard class="mb-4">
      <VCardTitle>低代码路由测试</VCardTitle>
      <VCardText>
        <p>测试低代码路由拦截和统一容器功能</p>
      </VCardText>
    </VCard>

    <!-- 测试链接 -->
    <VCard class="mb-4">
      <VCardTitle>测试链接</VCardTitle>
      <VCardText>
        <div class="test-links">
          <VBtn 
            v-for="link in testLinks" 
            :key="link.path"
            :to="link.path"
            color="primary"
            class="ma-2"
          >
            {{ link.name }}
          </VBtn>
        </div>
      </VCardText>
    </VCard>

    <!-- 配置管理器状态 -->
    <VCard class="mb-4">
      <VCardTitle>配置管理器状态</VCardTitle>
      <VCardText>
        <VBtn @click="showCacheStatus" color="info" class="ma-2">
          查看缓存状态
        </VBtn>
        <VBtn @click="clearAllCache" color="warning" class="ma-2">
          清除所有缓存
        </VBtn>
      </VCardText>
    </VCard>

    <!-- 缓存状态显示 -->
    <VCard v-if="cacheStatus" class="mb-4">
      <VCardTitle>缓存状态</VCardTitle>
      <VCardText>
        <p><strong>缓存数量:</strong> {{ cacheStatus.size }}</p>
        <p><strong>缓存键:</strong></p>
        <ul>
          <li v-for="key in cacheStatus.keys" :key="key">{{ key }}</li>
        </ul>
      </VCardText>
    </VCard>

    <!-- 手动测试 -->
    <VCard class="mb-4">
      <VCardTitle>手动测试</VCardTitle>
      <VCardText>
        <VRow>
          <VCol cols="6">
            <VTextField
              v-model="testScenario"
              label="场景名称"
              placeholder="例如: user-management"
            />
          </VCol>
          <VCol cols="6">
            <VSelect
              v-model="testPageType"
              :items="pageTypes"
              label="页面类型"
            />
          </VCol>
        </VRow>
        <VBtn @click="testPageConfig" color="success" class="mt-4">
          测试页面配置
        </VBtn>
      </VCardText>
    </VCard>

    <!-- 测试结果 -->
    <VCard v-if="testResult" class="mb-4">
      <VCardTitle>测试结果</VCardTitle>
      <VCardText>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </VCardText>
    </VCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { pageConfigManager } from '@/business/low-code-engine/utils/pageConfigManager.js'

// 测试链接
const testLinks = [
  { path: '/low-code/user-management/list', name: '用户管理列表' },
  { path: '/low-code/user-management/create', name: '用户管理创建' },
  { path: '/low-code/user-management/edit', name: '用户管理编辑' },
  { path: '/low-code/product-catalog/list', name: '产品目录列表' },
  { path: '/low-code/order-system/list', name: '订单系统列表' },
]

// 页面类型
const pageTypes = ['list', 'create', 'edit', 'detail']

// 测试数据
const testScenario = ref('user-management')
const testPageType = ref('list')
const cacheStatus = ref(null)
const testResult = ref(null)

// 显示缓存状态
function showCacheStatus() {
  cacheStatus.value = pageConfigManager.getCacheStatus()
}

// 清除所有缓存
function clearAllCache() {
  pageConfigManager.clearCache()
  cacheStatus.value = null
  testResult.value = null
}

// 测试页面配置
async function testPageConfig() {
  try {
    testResult.value = null
    
    console.log('测试页面配置:', {
      scenario: testScenario.value,
      pageType: testPageType.value
    })
    
    // 获取页面配置
    const config = await pageConfigManager.getPageConfig(
      testScenario.value,
      testPageType.value
    )
    
    testResult.value = {
      success: true,
      config: config,
      cacheStatus: pageConfigManager.getCacheStatus()
    }
    
  } catch (error) {
    testResult.value = {
      success: false,
      error: error.message,
      cacheStatus: pageConfigManager.getCacheStatus()
    }
  }
}
</script>

<style lang="scss" scoped>
.low-code-test {
  padding: 20px;
  
  .test-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  pre {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
  }
}
</style> 