<script setup lang="jsx">
import { ref } from "vue";
import { dateUtil } from "@/utils/day";
import { useTable } from "@/hooks/useTable";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants.js";
import AppAutocomplete from "@/components/AppAutocomplete.vue";
import message from "@/utils/message.js";
import { confirmDialog } from "@/utils/dialog.js";
import { useRouter } from "vue-router";
import { hasPermission } from "@/directives/can.js";
import VuePdfEmbed from "vue-pdf-embed";
import {
  requiredValidator,
  merchantOnboardingListRemarkValidator,
  merchantOnboardingListReviewRemarkValidator,
} from "@/utils/validators";

import {
  VBtn,
  VCard,
  VForm,
  VCardText,
  VCardTitle,
  VCardItem,
  VRow,
  VCol,
} from "vuetify/components";

import { useMerchant } from "@/business/merchant-onboarding/merchant-onboarding-list/useMerchant.js";

import { useAuthStore } from "@/stores/useAuthStore";

import { debounce } from '@/utils/helpers'


const authStore = useAuthStore();

// keys are the fields from backend
const headers = ref([
  { title: "No", key: "no", fixed: true },
  { title: "Merchant Name", key: "name", sortable: false },
  { title: "Email", key: "merchantEmail", sortable: false },
  { title: "Status", key: "status", sortable: false },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    fixed: true,
    width: 120,
  },
]);

//*********************************************** define constant variables ***********************************************
const statusList = [
  {
    title: "Pending Activate",
    value: 1,
  },
  {
    title: "Pending Onboard",
    value: 2,
  },
  {
    title: "Pending KYB",
    value: 3,
  },
  {
    title: "KYB Success",
    value: 4,
  },
  {
    title: "Processing KYB",
    value: 5,
  },
  {
    title: "KYB Failed",
    value: 6,
  },
];

const resolveStatusVariant = (status, type = "color") => {
  const statusDefination = {
    PENDING_ACTIVATE: {
      color: "#FFA800",
      text: "Pending Activate",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    PENDING_ONBOARD: {
      color: "#FFA800",
      text: "Pending Onboard",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    PENDING_KYB: {
      color: "#FFA800",
      text: "Pending KYB",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    PROCESSING_KYB: {
      color: "#2962FF",
      text: "Processing KYB",
      icon: "mdi-circle-slice-4",
      size: "15px",
      iconStyle: { transform: "rotate(90deg)" },
    },
    KYB_FAILED: {
      color: "#d50000",
      text: "KYB Failed",
      icon: "mdi-alert-circle",
      size: "15px",
    },
    KYB_SUCCESS: {
      color: "#44d62c",
      text: "KYB Success",
      icon: "mdi-check-decagram",
      size: "15px",
    },
  };
  return statusDefination[status][type];
};

const STATUS_MAP = {
  PENDING_ACTIVATE: 1,
  PENDING_ONBOARD: 2,
  PENDING_KYB: 3,
  KYB_SUCCESS: 4,
  PROCESSING_KYB: 5,
  KYB_FAILED: 6,
};

const DISPLAY_STATUS_MAP = {
  1: "Pending Activate",
  2: "Pending Onboard",
  3: "Pending KYB",
  4: "KYB Success",
  5: "Processing KYB",
  6: "KYB Failed",
  default: "",
};

const UPPER_DISPLAY_STATUS_MAP = {
  1: "PENDING_ACTIVATE",
  2: "PENDING_ONBOARD",
  3: "PENDING_KYB",
  4: "KYB_SUCCESS",
  5: "PROCESSING_KYB",
  6: "KYB_FAILED",
  default: "",
};

const PDF_PREVIEW_DIALOG_TYPE = {
  PDF_DIALOG: 0,
  FILE_DIALOG: 1,
};

const PDF = [".pdf"];

const IMG = [".jpg", ".png"];

const TIFF = [".tiff"];

const enableViewFileStatus = [STATUS_MAP.PENDING_KYB, STATUS_MAP.PROCESSING_KYB, STATUS_MAP.KYB_SUCCESS, STATUS_MAP.KYB_FAILED];

const enableViewFile = (status) => enableViewFileStatus.includes(status);

const disableViewFile = (status) => !enableViewFileStatus.includes(status);

const enableDownloadStatus = [
  STATUS_MAP.PENDING_KYB,
  STATUS_MAP.KYB_SUCCESS,
  STATUS_MAP.PROCESSING_KYB,
  STATUS_MAP.KYB_FAILED,
];

const enableDownload = (status) => enableDownloadStatus.includes(status);

const hasSomeElements = (str, elements) => {
  if (isEmpty(str)) return false;
  return elements.some((el) => str.includes(el));
};

const OPERATION_TYPE = {
  APPROVE: 4,
  REVIEW: 5,
  REJECT: 6,
};

const REJECT_REASONS = [
  "1. Sanctioned country/individual",
  "2. Refuse to provide mandatory KYB documents",
  "3. Game/App contains direct cash-out feature",
  "4. Incorporation of Prohibited Activities",
  "5. Others"
];

const REVIEW_SECTION = [
  "1. Merchant (Company)",
  "2. Business Information",
  "3. Game / Application",
  "4. Authorized Signatory",
  "5. Company Director",
  "6. Ultimate Business Owner",
  "7. Bank Account",
  "8. Additional Documents",
];

const REVIEW_REASON = [
  "1. Expired document",
  "2. Blurry image/document",
  "3. Masked image/document",
  "4. Wrong selection",
  "5. Incorrect information",
  "6. Typographical error",
  "7. Others"
];

const { merchantList } = useMerchant("");

//*********************************************** custom query params ***********************************************
const customGetQueryParams = () => {
  const params = {};
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.merchantOnboardIds = searchQuery.value.merchantOnboardIds;
  params.statusCodes = searchQuery.value.statusCodes;
  params.merchantEmail = searchQuery.value.merchantEmail;

  return params;
};

//*********************************************** use useTable hook to handle table data ***********************************************
const {
  searchQuery,
  page,
  itemsPerPage,
  items: merchantOnboardingList,
  total: totalMerchantOnboardingList,
  isLoading,
  handlePageChange,
  handleItemsPerPageChange,
  handleSearch,
  handleSortChange,
  loadData,
} = useTable({
  fetchData() {
    return $api("/api/merchant-onboard/v1/search", {
      method: "POST",
      data: customGetQueryParams(),
    });
  },
  createDefaultQuery: () => ({
    merchantOnboardIds: null,
    statusCodes: null,
    merchantEmail: null,
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null,
  }),
  rowKey: "merchantOnboardId",
});

//*********************************************** Handle create new merchant ***********************************************
const router = useRouter();

const createNewMerchant = () => {
  router.push(
    "/merchant-onboarding/merchant-onboard-list/merchant/add"
  );
};

//*********************************************** Handle pdf preview ***********************************************
const pdfDialog = ref(false);
const fileDialog = ref(false);
const pdfSource = ref("");
const fileSource = ref("");
const fileNameTemp = ref("");
const isLoadingPdf = ref(false);
const merchantId = ref(null);
const merchantOnboardingVersion = ref(null);
const merchantName = ref(null);
const merchantEmail = ref(null);
const operationBtnFlag = ref(false);

const tiffPages = ref([]);

// Initialize the preview dialog
const initPreviewDialog = async (item, fileId, previewDialogType, fileName) => {

  const approvePermission = hasPermission('Approve', 'MerchantOnboardingList');
  fileNameTemp.value = decodeURI(fileName);
  document.addEventListener("keydown", disableDevTools);
  document.addEventListener("copy", disableCopy);
  document.addEventListener("contextmenu", disableRightClick);

  // Open pdf dialog
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    merchantId.value = item?.merchantOnboardId;
    merchantOnboardingVersion.value = item?.version;
    merchantName.value = item?.name;
    merchantEmail.value = item?.merchantEmail;
    pdfDialog.value = true;
    pdfSource.value = "";
    isLoadingPdf.value = true;
    operationBtnFlag.value = item.status === STATUS_MAP.PENDING_KYB && approvePermission;
  } else {
    // Open file dialog
    fileDialog.value = true;
    fileSource.value = "";
  }

  // Load the file
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    try {
      const pdfUrlApi = approvePermission ? `/api/merchant-onboard/v1/preview` : `/api/merchant-onboard/v1/download`;
      const generatingPdfRes = await $api(
        pdfUrlApi,
        {
          method: "GET",
          params: {
            merchantOnboardId: merchantId.value,
          },
          showToast: false
        }
      );

      if (generatingPdfRes.data) {
        pdfSource.value = JSON.parse(generatingPdfRes.data).presignedUrl;
      }
    } catch (error) {
      console.error(error);
      message.error("Failed to load preview file");
    }
  } else {
    // Load the document file
    try {
      const res = await $api(`/api/merchant-onboard/v1/presigned-url`, {
        method: "GET",
        params: {
          fileId: fileId,
        },
        showToast: false
      });

      if (res.data) {
        if (fileNameTemp.value.includes(TIFF[0])) {
          await parseTiff(JSON.parse(res.data).presignedUrl);
        } else {
          fileSource.value = JSON.parse(res.data).presignedUrl;
        }
      }
    } catch (error) {
      console.error(error);
      message.error("Failed to load document file");
    }
  }
};

const parseTiff = async (url) => {
  // Use the globally loaded Tiff object
  const Tiff = window.Tiff;
  Tiff.initialize({ TOTAL_MEMORY: 500 * 1024 * 1024 });
  
  const response = await fetch(url);
  const buffer = await response.arrayBuffer();
  const tiff = new Tiff({ buffer });
  const pages = [];
  for (let i = 0; i < tiff.countDirectory(); i++) {
    tiff.setDirectory(i);
    pages.push(tiff.toDataURL());
  }
  tiffPages.value = pages;
}

// Close the preview dialog
const closeDialog = (previewDialogType) => {
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    pdfDialog.value = false;
    closeFileDialog();
  } else {
    closeFileDialog();
  }

  document.removeEventListener("copy", disableCopy);
  document.removeEventListener("contextmenu", disableRightClick);
};


const closeFileDialog = () => {
  fileDialog.value = false;
};

// Handle the pdf rendered event
const handlePdfRendered = () => {
  isLoadingPdf.value = false;
  const links = document.querySelectorAll(".pdf-viewer a");
  links.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();

      const urlArray = e.target.href.split('/');
      const fileName = urlArray.length >= 1 ? urlArray[urlArray.length - 1] : null;
      const fileId = urlArray.length >= 2 ? urlArray[urlArray.length - 2] : null;

      initPreviewDialog(
        null,
        fileId,
        PDF_PREVIEW_DIALOG_TYPE.FILE_DIALOG,
        fileName
      );
    });
  });
};

// Handle the error event
const handlePreviewError = (e) => {
  isLoadingPdf.value = true;
  console.error(e);
  message.error("Failed to load preview file");
};

// Handle refersh pdf
const handlePdfRefresh = async () => {
  const temp = pdfSource.value;
  pdfSource.value = null;
  
  await nextTick();
  isLoadingPdf.value = true;
  pdfSource.value = temp;
};

const refreshPdf = debounce(handlePdfRefresh, 1000)

window.addEventListener('resize', refreshPdf)

// *********************************************** disable copy, right click and dev tools ***********************************************
const disableCopy = (e) => {
  e.preventDefault();
  e.clipboardData.setData("text/plain", "");
};

const disableDevTools = (e) => {
  if (
    e.key === "F12" ||
    (e.ctrlKey && e.shiftKey && e.key === "I") ||
    ((e.ctrlKey || e.metaKey) && e.key === "s")
  ) {
    e.preventDefault();
  }
};

const disableRightClick = (e) => {
  e.preventDefault();
};

//*********************************************** confirm approval handle function ***********************************************

const selectedReason = ref(null);
const remarks = ref(null);
const reviewRows = ref([
  { reviewSection: null, reviewReason: null, remarks: null },
]);
const isInProgress = ref(false);
const isInValid = ref(false);

const handleOperationClick = (type) => {
  selectedReason.value = null;
  remarks.value = null;
  reviewRows.value = [
    { reviewSection: null, reviewReason: null, remarks: null },
  ];
  const text =
    type === OPERATION_TYPE.APPROVE
      ? "Once approved, the merchant will be ready to be onboarded."
      : type === OPERATION_TYPE.REJECT
        ? "Once rejected, the merchant account will be auto disabled, and the entire merchant onboarding process will be terminated. This action cannot be undone."
        : type === OPERATION_TYPE.REVIEW
          ? "Once the review is submitted, the merchant will adjust the content according to your feedback and resubmit it for approval.You must enter at least 1 row of feedback details before submitting the review. You can also add more feedback details by clicking 'Add'."
          : "";

  const toastMsg =
    type === OPERATION_TYPE.APPROVE
      ? "Merchant Onboarding KYB is successfully approved"
      : type === OPERATION_TYPE.REJECT
        ? "Merchant Onboarding KYB is successfully rejected"
        : type === OPERATION_TYPE.REVIEW
          ? "Merchant Onboarding KYB is successfully sent for review"
          : "";

  const width =
    type === OPERATION_TYPE.APPROVE
      ? 400
      : type === OPERATION_TYPE.REJECT
        ? 600
        : type === OPERATION_TYPE.REVIEW
          ? 1000
          : 400;

  confirmDialog({
    title: `${type === OPERATION_TYPE.APPROVE
      ? "Approve Merchant Onboarding KYB?"
      : type === OPERATION_TYPE.REJECT
        ? "Reject Merchant Onboarding KYB?"
        : type === OPERATION_TYPE.REVIEW
          ? "Review Merchant Onboarding KYB?"
          : ""
      }`,
    text,
    width,
    render: () => {
      return (
        <div>
          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.APPROVE ? "block" : "none",
            }}
          >
            <AppTextField
              v-model={remarks.value}
              label="Remarks (Optional)"
              placeholder="Type a reason"
              rules={[merchantOnboardingListRemarkValidator]}
            />
          </div>
          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.REJECT ? "block" : "none",
            }}
          >
            <AppSelect
              items={REJECT_REASONS}
              label="Reject Reason*"
              placeholder="Type a reason"
              v-model={selectedReason.value}
              item-text="text"
              item-value="value"
              rules={[requiredValidator]}
            />
            <AppTextField
              v-model={remarks.value}
              label="Remarks (Optional)"
              placeholder="Type a reason"
              rules={[merchantOnboardingListRemarkValidator]}
            />
          </div>

          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.REVIEW ? "block" : "none",
            }}
          >
            <div style="max-height: 30vh;overflow-y: auto;padding: 15px; ">

              {reviewRows.value.map((row, index) => (

                <div key={index} style="margin-bottom:5px; ">
                  <VCard>
                    <VCardText style="background-color: #2D2D2D;">
                      <VForm>
                        <VRow key={index} class="d-flex gap-2" style="font-size:18px; position: relative;">
                          Feedback {index + 1}
                          <div onClick={() => handleDelReviewRowClick(index)} style="position: absolute;right: 0;"
                            style={{
                              display: index !== 0 ? "block" : "none",
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="review-trash-can">
                              <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z" />
                            </svg>
                          </div>

                        </VRow>
                        <VRow key={index} class="d-flex gap-2">
                          <VCol md="5" sm="6" cols="12">
                            <AppSelect
                              items={REVIEW_SECTION}
                              label="Review Section*"
                              placeholder="Select Review Section"
                              v-model={row.reviewSection}
                              item-text="text"
                              item-value="value"
                              rules={[requiredValidator]}
                            />
                          </VCol>
                          <VCol md="5" sm="6" cols="12">
                            <AppSelect
                              items={REVIEW_REASON}
                              label="Review Reason*"
                              placeholder="Select Review Reason"
                              v-model={row.reviewReason}
                              item-text="text"
                              item-value="value"
                              rules={[requiredValidator]}
                            />
                          </VCol>
                        </VRow>
                        <VRow key={index} class="d-flex gap-2">
                          <VCol md="10" sm="6" cols="12">
                            <AppTextField
                              v-model={row.remarks}
                              label="Remarks (Optional)"
                              placeholder="Type a reason"
                              rules={[merchantOnboardingListReviewRemarkValidator]}
                            />
                          </VCol>
                        </VRow>
                      </VForm>
                    </VCardText>
                  </VCard>
                </div>
              ))}
            </div>
            <div
              class="d-flex align-center flex-wrap gap-4"
              style="justify-content: center;align-items: center;margin-top:20px;"
            >
              <VBtn
                variant="outlined"
                color="#44D62C"
                style="border:2px solid #30961f;"
                disabled={reviewRows.value.length === 80}
                onClick={() => handleAddReviewRow()}
              >
                ADD
              </VBtn>
            </div>
          </div>
        </div>
      );
    },
    confirmButtonText: `${type === OPERATION_TYPE.APPROVE
      ? "APPROVE"
      : type === OPERATION_TYPE.REJECT
        ? "REJECT"
        : type === OPERATION_TYPE.REVIEW
          ? "REVIEW"
          : ""
      }`,
    confirmButtonColor: `${type === OPERATION_TYPE.APPROVE
      ? "#44D62C"
      : type === OPERATION_TYPE.REJECT
        ? "#FD4949"
        : type === OPERATION_TYPE.REVIEW
          ? "#FD8611"
          : ""
      }`,
    confirmButtonTextColor: "#000000",
    cancelButtonText: "CANCEL",
    onCancel: () => {
      isInProgress.value = false;
      isInValid.value = false
    },
    async onConfirm(close) {
      if (isInProgress.value) return;
      isInProgress.value = true;
      isInValid.value = false;

      if (type === OPERATION_TYPE.APPROVE) {
        if (remarks.value && remarks.value.length > 250) {
          message.error(
            "The Remark field length must be less than 250 characters"
          );
          return;
        }
      }

      if (type === OPERATION_TYPE.REJECT) {
        if (isEmpty(selectedReason.value)) {
          message.error("Must select Reject Reason");
          return;
        }

        if (remarks.value && remarks.value.length > 250) {
          message.error(
            "The Remark field length must be less than 250 characters"
          );
          return;
        }
      }

      const reviewRowsParams = [];
      if (type === OPERATION_TYPE.REVIEW) {
        for (let i = 0; i < reviewRows.value.length; i++) {
          const row = reviewRows.value[i];
          if (isEmpty(row.reviewSection)) {
            isInValid.value = true;
            message.error("Must select Review Section");
            break;
          }

          if (isEmpty(row.reviewReason)) {
            isInValid.value = true;
            message.error("Must select Review Reason");
            break;
          }

          if (row.remarks && row.remarks.length > 500) {
            isInValid.value = true;
            message.error("The remarks field length must be less than 500 characters");
            break
          }

          reviewRowsParams.push(
            {
              reviewSectionCode: REVIEW_SECTION.indexOf(row.reviewSection) + 1,
              reviewSection: row.reviewSection,
              reviewReasonCode: REVIEW_REASON.indexOf(row.reviewReason) + 1,
              reviewReason: row.reviewReason,
              remarks: row.remarks,
            }
          );
        }
      }

      if (isInValid.value) {
        isInProgress.value = false;
        return;
      }
      try {
        await $api("/api/merchant-onboard/v1/status", {
          method: "POST",
          data: {
            merchantOnboardId: merchantId.value,
            email: merchantEmail.value,
            name: merchantName.value,
            statusCode: type,
            reason:
              type === OPERATION_TYPE.REJECT
                ? JSON.stringify([{
                  reviewSectionCode: 0,
                  reviewReasonCode: REJECT_REASONS.indexOf(selectedReason.value) + 1,
                }])
                : type === OPERATION_TYPE.REVIEW
                  ? JSON.stringify(reviewRowsParams)
                  : null,
            remark: (type === OPERATION_TYPE.REJECT || type === OPERATION_TYPE.APPROVE) ? remarks.value : null,
            description:
              type === OPERATION_TYPE.REJECT
                ? "reject"
                : type === OPERATION_TYPE.REVIEW
                  ? "review"
                  : "approve",
            version: merchantOnboardingVersion.value,
          },
          showToast: false
        });
        message.success(toastMsg);
        close();
        closeDialog(PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG);
        loadData();
      } catch (error) {
        console.error(error);
        message.error("Failed to update status");
      } finally {
        isInProgress.value = false;
      }
    },
  });
};

const handleDelReviewRowClick = (rowIndex) => {
  confirmDialog({
    title: `Remove Feedback ${rowIndex + 1}?`,
    text: 'Once removed, this action cannot be undone.',
    confirmButtonText: 'REMOVE',
    confirmButtonColor: 'primary',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      close()
      handleDelReviewRow(rowIndex)
    }
  })
};

const handleAddReviewRow = function () {
  reviewRows.value.push({
    reviewSection: null,
    reviewReason: null,
    remarks: null,
  });
};

const handleDelReviewRow = function (rowIndex) {
  reviewRows.value.splice(rowIndex, 1);
};

//*********************************************** download handle function ***********************************************

const handleDownloadFile = async (item) => {
  try {
    const generatingPdfRes = await $api(
      `/api/merchant-onboard/v1/download`,
      {
        method: "GET",
        params: {
          merchantOnboardId: item.merchantOnboardId,
        },
        showToast: false
      }
    );

    if (generatingPdfRes.data) {
      await downloadByBlob(JSON.parse(generatingPdfRes.data).presignedUrl, "merchantOnobardPreview.pdf");

      message.success("Download in progress, and will begin shortly");
    }
  } catch (error) {
    console.error(error);
    message.error("Failed to download file");
  }
};

const downloadByBlob = async (url, fileName) => {
  const response = await fetch(url);
  const blob = await response.blob();
  const objectURL = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = objectURL;
  link.download = fileName
  link.click();
  URL.revokeObjectURL(objectURL);
};

const isModalVisible = ref(false);
const isPanelBVisible = ref(false);
const openModal = () => {
  isModalVisible.value = true
};
const closeModal = () => {
  isModalVisible.value = false
  isPanelBVisible.value = false
};
const showPanelB = () => {
  isPanelBVisible.value = true
};

</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Merchant Onboarding List
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn v-can="['Add', 'MerchantOnboardingList']" @click.stop="createNewMerchant">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Select Merchant Onboarding Name -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.merchantOnboardIds" placeholder="Select Merchant Name"
              :items="merchantList" clearable clear-icon="tabler-x" multiple filterable chips closable-chips
              outline-label="Merchant Name" @update:model-value="handleSearch" />
          </VCol>

          <!-- 👉 Search Email -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField v-model="searchQuery.merchantEmail" placeholder="Search Email" outline-label="Email" clearable
              @update:model-value="handleSearch" prepend-inner-icon="mdi-magnify" />
          </VCol>

          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.statusCodes" placeholder="Select Status" :items="statusList" clearable
              outline-label="Status" clear-icon="tabler-x" multiple filterable chips closable-chips
              @update:model-value="handleSearch" />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer :items="merchantOnboardingList" item-value="merchantOnboardId"
        :items-length="totalMerchantOnboardingList" :headers="headers" :loading="isLoading" class="text-no-wrap">
        <!-- Merchant Onboarding Name -->
        <template #[`item.name`]="{ item }">
          <div v-if="enableViewFile(item.status)">
            <AppText @click.prevent="initPreviewDialog(
              item,
              null,
              PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG,
              null
            )" :auth="['View', 'MerchantOnboardingList']">
              {{ item.name }}
            </AppText>
          </div>
          <div v-if="disableViewFile(item.status)">
            {{ item.name }}
          </div>

        </template>

        <!-- Merchant Email -->
        <template #[`item.merchantEmail`]="{ item }">
          {{ item.merchantEmail }}
        </template>

        <!-- Status -->
        <template #[`item.status`]="{ item }">
          <span class="d-flex align-center">
            <VIcon :icon="resolveStatusVariant(
              UPPER_DISPLAY_STATUS_MAP[item.status],
              'icon'
            )
              " :color="resolveStatusVariant(
                UPPER_DISPLAY_STATUS_MAP[item.status],
                'color'
              )
                " :size="resolveStatusVariant(
                  UPPER_DISPLAY_STATUS_MAP[item.status],
                  'size'
                )
                  " :style="resolveStatusVariant(
                    UPPER_DISPLAY_STATUS_MAP[item.status],
                    'iconStyle'
                  ) || {}
                    " />
            <span class="ms-1">{{ DISPLAY_STATUS_MAP[item.status] }}</span>
            <VTooltip v-if="item.status === STATUS_MAP.KYB_FAILED && item.rejectReason" location="top">
              <template #activator="{ props }">
                <VIcon v-bind="props" icon="mdi-help-circle-outline" size="15" class="ms-1" />
              </template>
              <span>
                {{ item.rejectReason }}
                <span v-if="item.remark">:{{ item.remark }}</span>
              </span>
            </VTooltip>
          </span>
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <div class="actions-grid">
            <div class="action-item">
              <IconBtn v-can="['Approve', 'MerchantOnboardingList']" @click.stop="
                initPreviewDialog(
                  item,
                  null,
                  PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG,
                  null
                )
                " :disabled="!enableViewFile(item.status)" v-tooltip="'Review & Approve'">
                <VIcon icon="mdi-file-document-edit" :class="enableViewFile(item.status)
                  ? 'hover:text-primary'
                  : 'text-disable'
                  " />
              </IconBtn>
              <IconBtn v-can="['Download', 'MerchantOnboardingList']" @click.stop="handleDownloadFile(item)"
                :disabled="!enableDownload(item.status)" v-tooltip="'Download'">
                <VIcon icon="mdi-download-outline" :class="enableDownload(item.status)
                  ? 'hover:text-primary'
                  : 'text-disable'
                  " />
              </IconBtn>
            </div>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination :page="page" :items-per-page="itemsPerPage" :total-items="totalMerchantOnboardingList"
            @update:page="handlePageChange">
            <div class="d-flex gap-3">
              <AppSelect :model-value="itemsPerPage" :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange" />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>

    <div v-show="pdfDialog" class="dialog-mask">
      <div class="panel-a">
        <div class="pdf-box">
          <v-card>
            <div class="pdf-drag-handle">
              <v-card-title class="d-flex justify-space-between align-center">
                <span>Preview Document</span>
                <v-icon @click.stop="closeDialog(PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG)">mdi-close-circle</v-icon>
              </v-card-title>
            </div>
            <div class="pdf-content">
              <v-card-text class="pa-0">
                <div class="pdf-viewer no-print">
                  <div v-if="isLoadingPdf">Loading...</div>
                  <vue-pdf-embed v-if="pdfDialog" :source="pdfSource" @rendered="handlePdfRendered" text-layer
                    annotationLayer @loading-failed="handlePreviewError" @rendering-failed="handlePreviewError" />
                </div>
              </v-card-text>
            </div>
            <div :class="[operationBtnFlag ? 'pdf-footer' : 'pdf-footer-plus']">
              <VRow class="d-flex justify-end gap-2" v-if="operationBtnFlag">
                <VBtn :disabled="isLoadingPdf" variant="outlined" color="default"
                  @click.stop="handleOperationClick(OPERATION_TYPE.REJECT)">
                  REJECT
                </VBtn>
                <VBtn :disabled="isLoadingPdf" color="warning" class="review-btn"
                  @click.stop="handleOperationClick(OPERATION_TYPE.REVIEW)">
                  REVIEW
                </VBtn>
                <VBtn :disabled="isLoadingPdf" @click.stop="handleOperationClick(OPERATION_TYPE.APPROVE)">
                  APPROVE
                </VBtn>
              </VRow>
            </div>
          </v-card>
        </div>
      </div>
      <div class="panel-b" v-show="fileDialog">
        <div class="file-box" v-show="fileDialog">
          <v-card>
            <div class="file-drag-handle">
              <v-card-title class="d-flex justify-space-between align-center">
                <span :title="fileNameTemp" class="file-title">
                  {{ fileNameTemp }}
                </span>
                <v-icon @click.stop="closeDialog(PDF_PREVIEW_DIALOG_TYPE.FILE_DIALOG)">mdi-chevron-left</v-icon>
              </v-card-title>
            </div>
            <div class="pdf-content">
              <div v-if="hasSomeElements(fileNameTemp, PDF) && fileSource" class="file-viewer no-print">
                <vue-pdf-embed :source="fileSource" @loading-failed="handlePreviewError"
                  @rendering-failed="handlePreviewError" />
              </div>
              <div v-if="hasSomeElements(fileNameTemp, IMG) && fileSource" class="file-viewer no-print">
                <img :src="fileSource" @error="handlePreviewError" class="img-file-viewer" />
              </div>
              <div v-if="hasSomeElements(fileNameTemp, TIFF) && tiffPages && tiffPages.length > 0" class="file-viewer no-print">
                <div v-for="(page, idx) in tiffPages" :key="idx">
                  <img :src="page" @error="handlePreviewError" class="img-file-viewer" />
                </div>
              </div>
            </div>
          </v-card>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped>
.actions-grid {
  display: grid;
  grid-template-columns: 40px 40px;
  gap: 8px;
}

.action-item {
  display: flex;
  justify-content: center;
}

:deep(.v-data-table) {
  .v-table__wrapper {
    overflow: auto;
  }

  tr:has(.tabler-chevron-up) .v-data-table-column--last-fixed {
    border-bottom: none;
  }

  .v-data-table-column--last-fixed {
    right: 0;
    border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}

.pdf-box {
  position: fixed;
  z-index: 2000;
  border: 1px solid #111111;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease-out;
  will-change: transform;
  width: 40vw;
  height: 90vh;
  transform: translate(0px, 5px);
  overflow: auto;
  padding-right: 2px;
}

.file-box {
  position: fixed;
  z-index: 2001;
  border: 1px solid #111111;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease-out;
  will-change: transform;
  width: 35vw;
  height: 90vh;
  transform: translate(0px, 5px);
  overflow: auto;
  padding-right: 2px;
}

.pdf-drag-handle {
  padding: 16px 0px;
  background: #222222;
}

.file-drag-handle {
  padding: 16px 0px;
  background: #222222;
}

.file-title {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pdf-content {
  padding: 16px 16px;
  height: 100%;
  overflow: auto;
  border: 1px solid #111111;
  background: #111111;
}

.pdf-footer {
  padding: 36px 16px 36px 16px;
  border: 1px solid #222222;
  background: #222222;
}

.pdf-footer-plus {
  padding: 42px 16px 42px 16px;
  border: 1px solid #222222;
  background: #222222;
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
}

.pdf-viewer {
  height: 71vh;
  padding: 16px 16px;
}

.file-viewer {
  height: 79vh;
  padding: 8px 8px;
}

.img-file-viewer {
  width: -webkit-fill-available;
  height: -webkit-fill-available;
}

/* disable print */
@media print {
  body * {
    visibility: hidden !important;
  }
}

/* disable print */
@media print {
  .no-print {
    display: none !important;
  }
}

.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

.form-container {
  width: 100%;
  margin-bottom: 20px;
}

.row {
  display: flex;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.field {
  flex: 1;
  margin-right: 10px;
}

.field input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

.actions {
  width: 100px;
  display: flex;
  align-items: center;
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

button {
  padding: 8px 12px;
  margin-right: 10px;
  cursor: pointer;
}

.review-btn {
  color: #000000 !important;
}

.panel-a {
  width: 40vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.panel-b {
  width: 35vw;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style lang="scss">
.review-trash-can {
  width: 24px;
  height: 24px;
  fill: #999999;
}

.review-trash-can:hover {
  width: 24px;
  height: 24px;
  fill: #FFFFFF;
}

.review-trash-can:active {
  width: 24px;
  height: 24px;
  fill: #44D62C;
}

canvas{
  margin-bottom: 10px;
}
</style>