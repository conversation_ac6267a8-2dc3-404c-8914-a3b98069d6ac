// Windows path fix for ESM loader
export async function resolve(specifier, context, defaultResolve) {
  // Handle Windows absolute paths (C:/, D:/, etc.)
  if (typeof specifier === 'string' && specifier.match(/^[a-zA-Z]:[/\\]/)) {
    // 简单的路径转换，不使用 pathToFileURL
    specifier = 'file:///' + specifier.replace(/\\/g, '/');
  }

  return defaultResolve(specifier, context);
}

export async function load(url, context, defaultLoad) {
  return defaultLoad(url, context);
}
