import {
  STATUS_MAP,
  SUPER_ADMIN_GROUP_ID,
  RESELLER_ONBOARD_GROUP_ID,
  SUPER_ADMIN_ROLE_ID,
  RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID,
  RESELLER_ONBOARD_COMPLIANCE_ENQUIRY_ROLE_ID,
  RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID,
  enableEnquiryViewStatus,
  enableKYBViewStatus,
  APPROVE_ACCESS,
  RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID,
  RESELLER_TYPE,
  RESELLER_TIER_TYPE,
  EXPECTED_SALES_PER_MONTH,

} from "@/business/reseller-onboarding/reseller-onboarding-list/constants.js";

import {
  hasRole,
  hasActionPermission,
} from "@/business/reseller-onboarding/reseller-onboarding-list/utils.js";

export const enableLinkView = (roleAccessList, status) => {
  return enableSuperAdminLinkView(roleAccessList, status) ||
    enableEnquiryView(roleAccessList, status) ||
    enableKYBView(roleAccessList, status);
};

export const enableSuperAdminLinkView = (roleAccessList, status) => {
  const statusFlag = enableEnquiryViewStatus.includes(status) || enableKYBViewStatus.includes(status);
  const roleFlag = hasRole(roleAccessList,
    SUPER_ADMIN_GROUP_ID,
    [
      SUPER_ADMIN_ROLE_ID
    ]);
  return statusFlag && roleFlag;
};

export const enableEnquiryView = (roleAccessList, status) => {
  const statusFlag = enableEnquiryViewStatus.includes(status);
  const roleFlag = hasRole(roleAccessList,
    RESELLER_ONBOARD_GROUP_ID,
    [
      RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID,
      RESELLER_ONBOARD_COMPLIANCE_ENQUIRY_ROLE_ID,
    ]);
  return statusFlag && roleFlag;
};

export const enableKYBView = (roleAccessList, status) => {
  const statusFlag = enableKYBViewStatus.includes(status);
  const roleFlag = hasRole(roleAccessList,
    RESELLER_ONBOARD_GROUP_ID,
    [
      RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID,
    ]);
  return statusFlag && roleFlag;
};

export const enableEnquiryApprove = (roleAccessList, statusCode) => {
  const superAdminFlag = hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, APPROVE_ACCESS);
  if (superAdminFlag && STATUS_MAP.ENQUIRY_SUBMITTED === statusCode) {
    return true;
  }

  const resellerOpsFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID, APPROVE_ACCESS);
  if (resellerOpsFlag && STATUS_MAP.ENQUIRY_SUBMITTED === statusCode) {
    return true;
  }

  const countryManagerFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID, APPROVE_ACCESS);
  if (countryManagerFlag && STATUS_MAP.ENQUIRY_SUBMITTED === statusCode) {
    return true;
  }
  return false;
};

export const enableKYBApprove = (roleAccessList, statusCode) => {
  const superAdminFlag = hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, APPROVE_ACCESS);
  if (superAdminFlag && STATUS_MAP.PENDING_KYB === statusCode) {
    return true;
  }

  const complianceFlag = hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID, APPROVE_ACCESS);
  if (complianceFlag && STATUS_MAP.PENDING_KYB === statusCode) {
    return true;
  }
  return false;
};

export const resellerTierTypeCheck = (resellerType, expectedSalesPerMonth) => {
  if (resellerType === RESELLER_TYPE.INDIVIDUAL) {
    return RESELLER_TIER_TYPE.LOW;
  } else {
    if (expectedSalesPerMonth === EXPECTED_SALES_PER_MONTH.BELOW_20K || expectedSalesPerMonth === EXPECTED_SALES_PER_MONTH.TO_20K_100K) {
      return RESELLER_TIER_TYPE.MEDIUM;
    } else {
      return RESELLER_TIER_TYPE.HIGH;
    }
  }
};