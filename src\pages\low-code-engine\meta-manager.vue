<template>
  <LowCodeLayout navigation-value="metadata-manager">
    <div class="meta-manager">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">Metadata Manager</h1>
        <p class="page-subtitle">Define business scenarios and field structures</p>
      </div>

      <!-- Business Scenarios 区域 -->
      <div class="scenarios-section">
        <div class="section-header">
          <div class="section-title">
            <VIcon class="mr-2" color="success">mdi-database</VIcon>
            Business Scenario
          </div>
        </div>
        <VRow>
          <VCol cols="12" md="4">
            <AppAutocomplete v-model="selectedScenario" :items="scenarios" item-title="scenario" item-value="scenario"
              return-object label="Select Scenario" filterable variant="outlined" density="compact" class="mb-4"
              hide-details />
            <div v-if="selectedScenario" :key="selectedScenario.id" class="scenario-card selected">
              <h3 class="scenario-name">{{ selectedScenario.scenario }}</h3>
              <p class="scenario-description">{{ selectedScenario.description }}</p>
              <div class="scenario-stats">
                <span class="stat-item">
                  <VIcon size="12">mdi-format-list-bulleted</VIcon>
                  {{ selectedScenario.fields ? selectedScenario.fields.length : 0 }} fields
                </span>
                <span class="stat-item">
                  <VIcon size="12">mdi-key</VIcon>
                  {{ selectedScenario.indexes ? selectedScenario.indexes.length : 0 }} indexes
                </span>
                <span class="stat-item">
                  <VIcon size="12">mdi-clock</VIcon>
                  {{ selectedScenario.lastModified }}
                </span>
              </div>
            </div>
          </VCol>
        </VRow>
      </div>

      <!-- Field Configuration 区域 -->
      <div v-if="selectedScenario" class="field-config-section">
        <div class="section-header">
          <div class="section-title">
            Field Configuration - {{ selectedScenario.scenario }}
          </div>
          <div class="action-buttons">
            <VBtn variant="outlined" @click="generatePages">
              <VIcon class="mr-1">mdi-auto-fix</VIcon>
              Generate Pages
            </VBtn>
            <VBtn variant="flat" color="primary" @click="addFieldDialog = true">
              <VIcon class="mr-1">mdi-plus</VIcon>
              Add Field
            </VBtn>
          </div>
        </div>

        <!-- 字段配置表格 -->
        <div class="table-container">
          <VDataTable :headers="fieldHeaders" :items="selectedScenario.fields" :items-per-page="10"
            class="field-table" item-key="fieldName" hide-default-footer>
            <template #item.fieldName="{ item }">
              <code class="field-name-code">{{ item.fieldName }}</code>
            </template>

            <template #item.fieldType="{ item }">
              <VChip size="small" variant="flat" class="type-chip">
                {{ item.layout.component }}
              </VChip>
            </template>

            <template #item.required="{ item }">
              <VChip v-if="item.required" size="small" color="error" variant="flat">
                Required
              </VChip>
              <span v-else class="text-disabled">-</span>
            </template>

            <template #item.queryable="{ item }">
              <VChip v-if="item.queryable" size="small" color="success" variant="flat">
                Queryable
              </VChip>
              <span v-else class="text-disabled">-</span>
            </template>

            <template #item.defaultValue="{ item }">
              <span v-if="item.defaultValue" class="default-value">
                {{ item.defaultValue }}
              </span>
              <span v-else class="text-disabled">-</span>
            </template>

            <template #item.actions="{ item }">
              <div class="action-icons">
                <VBtn icon size="small" variant="text" color="success" @click="editField(item)">
                  <VIcon size="16">mdi-pencil</VIcon>
                </VBtn>
                <VBtn icon size="small" variant="text" color="error" @click="deleteField(item)">
                  <VIcon size="16">mdi-delete</VIcon>
                </VBtn>
              </div>
            </template>
          </VDataTable>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <VIcon size="64" color="#666" class="mb-4">mdi-database-outline</VIcon>
        <h3 class="empty-title">Select a Scenario</h3>
        <p class="empty-subtitle">
          Choose a business scenario above to view and manage its field configuration
        </p>
      </div>



      <!-- 添加字段对话框 -->
      <FieldDialog v-model="addFieldDialog" mode="add" :form-data="newField" :field-types="fieldTypes"
        :loading="addingField" @submit="handleAddField" />

      <!-- 编辑字段对话框 -->
      <FieldDialog v-model="editFieldDialog" mode="edit" :form-data="editingField" :field-types="fieldTypes"
        :loading="editingFieldLoading" @submit="handleEditField" />

    </div>
  </LowCodeLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLowCodeAPI } from '@/business/low-code-engine/api/useLowCodeAPI'
import LowCodeLayout from '@/business/low-code-engine/layout/LowCodeLayout.vue'
import { useScenarioConfig } from '@/business/low-code-engine/hooks/useScenarioConfig'
import FieldDialog from '@/business/low-code-engine/meta-manager/FieldDialog.vue'
import { dateUtil } from '@/utils/day'
import { generatePagesFromBackendMeta } from '@/business/low-code-engine/editor/pageGenerator'
import { usePageGeneration } from '@/business/low-code-engine/hooks/usePageGeneration'

definePage({
  alias: '/low-code-engine/meta-manager',
  name: 'low-code-engine-meta-manager',
  meta: {
    layout: 'blank',
    public: true,
  },
})

// ===== 状态 =====
const router = useRouter()
const {
  addMetaField,
  getMetadata,
  createPage
} = useLowCodeAPI()

const { savePageContent, getPageContent } = usePageGeneration()

const { scenarioConfigs, selectedScenario, setCurrentScenario } = useScenarioConfig()

const createScenarioDialog = ref(false)
const addFieldDialog = ref(false)
const editFieldDialog = ref(false)
const addingField = ref(false)
const editingFieldLoading = ref(false)

// 场景数据
const scenarios = ref([])

watchEffect(() => {
  scenarios.value = Array.from(scenarioConfigs.value.values())
  if (!selectedScenario.value) {
    if (scenarios.value.length > 0) {
      setCurrentScenario(scenarios.value[0])
    }
  }
})

watch(selectedScenario, (newVal) => {
  if (newVal) {
    getMetaFields()
  }
}, { immediate: true })



// 新增字段表单
const newField = reactive({
  fieldName: '',
  displayName: '',
  fieldType: 'TEXT',
  defaultValue: '',
  required: false,
  queryable: false
})

// 编辑字段表单
const editingField = reactive({
  fieldName: '',
  displayName: '',
  fieldType: 'TEXT',
  source: '',
  items: [],
  defaultValue: '',
  required: false,
  queryable: false
})

// 表格配置
const fieldHeaders = [
  { title: 'Field Name', key: 'name', sortable: true },
  { title: 'Display Name', key: 'displayName', sortable: true },
  { title: 'Type', key: 'fieldType', sortable: true },
  { title: 'Required', key: 'required', sortable: false },
  { title: 'Queryable', key: 'queryable', sortable: false },
  { title: 'Default Value', key: 'defaultValue', sortable: false },
  // { title: 'Actions', key: 'actions', sortable: false }
]

// 字段类型选项
const fieldTypes = [
  'TEXT',
  'SELECT',
  'NUMBER',
  'SWITCH',
  'DATETIME',
]

// ===== 方法 =====



async function getMetaFields() {
  const response = await getMetadata({ scenario: selectedScenario.value.scenario })
  selectedScenario.value.fields = response.data.fields
  selectedScenario.value.fields = response.data.fields || []
  selectedScenario.value.indexes = response.data.indexes || [] 
  const lastModified = response.data.updatedDateTime ? response.data.updatedDateTime : response.data.createdDateTime
  selectedScenario.value.lastModified = dateUtil.format(lastModified, 'YYYY-MM-DD HH:mm:ss')
}

async function generatePages() {
  router.push({
    path: '/low-code-engine/editor',
    query: {
      scenario: selectedScenario.value.scenario
    }
  })
}


/**
 * 添加字段
 */
async function handleAddField(formData) {

  if (!selectedScenario.value) return

  addingField.value = true
  try {
    await addMetaField([{
      scenarioId: selectedScenario.value.id,
      name: formData.fieldName,
      displayName: formData.displayName,
      layout: {
        component: formData.fieldType,
        source: formData.source,
        items: formData.items.map(item => ({
          label: item.label,
          value: item.value
        }))
      },
      required: formData.required,
      queryable: formData.queryable,
      defaultValue: formData.defaultValue,
    }])

    // 刷新字段列表
    getMetaFields()

    // 关闭对话框 (重置表单由FieldDialog内部处理)
    addFieldDialog.value = false
  } catch (error) {
    console.error('添加字段失败:', error)
  } finally {
    addingField.value = false
  }
}

/**
 * 编辑字段
 */
function editField(field) {
  Object.assign(editingField, {
    fieldName: field.name,
    displayName: field.displayName,
    fieldType: field.layout.component,
    required: field.required,
    queryable: field.queryable,
  })
  editFieldDialog.value = true
}

/**
 * 更新字段
 */
async function handleEditField(formData) {
  editingFieldLoading.value = true
  try {
    // 更新本地数据
    const index = selectedScenario.value.fields.findIndex(
      f => f.fieldName === formData.fieldName
    )
    if (index !== -1) {
      Object.assign(selectedScenario.value.fields[index], formData)
    }

    editFieldDialog.value = false
  } catch (error) {
    console.error('更新字段失败:', error)
  } finally {
    editingFieldLoading.value = false
  }
}

/**
 * 删除字段
 */
function deleteField(field) {
  if (confirm(`确定要删除字段 "${field.fieldName}" 吗？`)) {
    const index = selectedScenario.value.fields.findIndex(
      f => f.fieldName === field.fieldName
    )
    if (index !== -1) {
      selectedScenario.value.fields.splice(index, 1)
      selectedScenario.value.fields = selectedScenario.value.fields.length
    }
  }
}



</script>

<style scoped>
.meta-manager {
  padding: 24px;
  background: #0f0f0f;
  min-height: calc(100vh - 68px);
  color: white;
}

/* 页面标题 */
.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #aaa;
  font-size: 1rem;
  margin: 0;
}

/* 区域样式 */
.scenarios-section {
  margin-bottom: 32px;
}

.field-config-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #333;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 场景选择 */
.scenario-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.scenario-card.selected {
  border-color: #4caf50;
  background: #202d23;
}

.scenario-name {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.scenario-description {
  color: #ccc;
  font-size: 0.875rem;
  margin: 0 0 16px 0;
  line-height: 1.5;
  min-height: 40px;
}

.scenario-stats {
  display: flex;
  gap: 24px;
  font-size: 0.8rem;
  color: #888;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 表格样式 */
.table-container {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.field-table) {
  background: #1a1a1a !important;
  color: white !important;
}

:deep(.field-table .v-data-table__thead) {
  background: #111 !important;
}

:deep(.field-table .v-data-table-header__content) {
  color: white !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

:deep(.field-table .v-data-table__td) {
  border-bottom: 1px solid #333 !important;
  color: white !important;
  padding: 12px 16px !important;
}

:deep(.field-table .v-data-table__tr:hover) {
  background: rgba(76, 175, 80, 0.05) !important;
}

.field-name-code {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.875rem;
}

/* 字段类型芯片样式 */
.type-chip {
  background-color: #333 !important;
  color: #ccc !important;
  border: 1px solid #555 !important;
}

.text-disabled {
  color: #666;
}

.default-value {
  color: #aaa;
  font-style: italic;
}

.action-icons {
  display: flex;
  gap: 4px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 64px 24px;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  margin-top: 24px;
}

.empty-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.empty-subtitle {
  color: #666;
  font-size: 0.875rem;
  margin: 0;
}

/* 对话框样式 */
:deep(.dialog-card) {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
}

:deep(.dialog-header) {
  background: #1a1a1a !important;
  color: white !important;
  border-bottom: 1px solid #333 !important;
}

:deep(.dialog-content) {
  background: #1a1a1a !important;
  color: white !important;
  padding: 24px !important;
  /* Increased padding */
  min-height: 300px;
  /* Added min-height */
}

:deep(.dialog-actions) {
  background: #1a1a1a !important;
  border-top: 1px solid #333 !important;
}

/* 表单控件样式 */
:deep(.v-text-field .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 6px !important;
}

:deep(.v-text-field .v-field__input) {
  color: white !important;
}

:deep(.v-text-field .v-label) {
  color: #888 !important;
}

:deep(.v-select .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 6px !important;
}

:deep(.v-select .v-field__input) {
  color: white !important;
}

.switches-row {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

:deep(.v-switch .v-label) {
  color: white !important;
}

/* 按钮样式 */
:deep(.v-btn) {
  text-transform: none;
  font-weight: 500;
}

:deep(.v-btn--variant-outlined) {
  border-color: #4caf50 !important;
  color: #4caf50 !important;
}

:deep(.v-btn--variant-outlined:hover) {
  background: rgba(76, 175, 80, 0.1) !important;
}

/* 芯片样式 */
:deep(.v-chip) {
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
}
</style>
