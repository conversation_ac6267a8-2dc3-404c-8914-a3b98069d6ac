/**
 * 低代码表单页面布局样式
 * 实现响应式宽度和居中布局效果
 */

/* 表单容器基础样式 */
.form-container-narrow,
.form-container-medium,
.form-container-wide {
  margin: 0 auto;
  transition: all 0.3s ease;
}

/* 窄容器 - 适用于字段较少的表单（1-3个字段） */
.form-container-narrow {
  max-width: 40%;
  min-width: 400px;
}

/* 中等容器 - 适用于中等字段数量的表单（4-8个字段） */
.form-container-medium {
  max-width: 60%;
  min-width: 600px;
}

/* 宽容器 - 适用于字段较多的表单（9个以上字段） */
.form-container-wide {
  max-width: 80%;
  min-width: 800px;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .form-container-narrow {
    max-width: 50%;
    min-width: 350px;
  }
  
  .form-container-medium {
    max-width: 70%;
    min-width: 500px;
  }
  
  .form-container-wide {
    max-width: 90%;
    min-width: 600px;
  }
}

@media (max-width: 960px) {
  .form-container-narrow,
  .form-container-medium,
  .form-container-wide {
    max-width: 95%;
    min-width: 300px;
  }
}

@media (max-width: 600px) {
  .form-container-narrow,
  .form-container-medium,
  .form-container-wide {
    max-width: 100%;
    min-width: auto;
    padding: 0 16px;
  }
}

/* 表单卡片样式增强 */
.v-card.form-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 深色背景表单区域样式 */
.v-card[style*="background-color: #1e1e1e"] {
  border: 1px solid #333;
}

.v-card[style*="background-color: #1e1e1e"] .v-text-field .v-field {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #ffffff !important;
}

.v-card[style*="background-color: #1e1e1e"] .v-text-field .v-field__input {
  color: #ffffff !important;
}

.v-card[style*="background-color: #1e1e1e"] .v-label {
  color: #cccccc !important;
}

.v-card[style*="background-color: #1e1e1e"] .v-text-field .v-field__placeholder {
  color: #888888 !important;
}

/* 选择器样式 */
.v-card[style*="background-color: #1e1e1e"] .v-select .v-field {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
}

/* 开关样式 */
.v-card[style*="background-color: #1e1e1e"] .v-switch .v-selection-control__wrapper {
  color: #ffffff !important;
}

/* 日期选择器样式 */
.v-card[style*="background-color: #1e1e1e"] .v-date-picker .v-field {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
}

/* 文本区域样式 */
.v-card[style*="background-color: #1e1e1e"] .v-textarea .v-field {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
}

.v-card[style*="background-color: #1e1e1e"] .v-textarea .v-field__input {
  color: #ffffff !important;
}

/* 表单字段间距 */
.form-field-spacing .v-col {
  padding-bottom: 16px;
}

/* 表单标题样式 */
.form-title-section {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 0;
}

/* 表单操作按钮区域 */
.form-actions-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 24px;
  margin-top: 0;
}

/* 按钮样式增强 */
.form-actions-section .v-btn {
  min-width: 120px;
  height: 40px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0.5px;
}

/* 动画效果 */
.form-container-narrow,
.form-container-medium,
.form-container-wide {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.form-loading {
  position: relative;
  overflow: hidden;
}

.form-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 错误状态样式 */
.v-card[style*="background-color: #1e1e1e"] .v-text-field.v-input--error .v-field {
  border-color: #ff5252 !important;
}

.v-card[style*="background-color: #1e1e1e"] .v-messages__message {
  color: #ff5252 !important;
}

/* 成功状态样式 */
.v-card[style*="background-color: #1e1e1e"] .v-text-field.v-input--success .v-field {
  border-color: #4caf50 !important;
}

/* 禁用状态样式 */
.v-card[style*="background-color: #1e1e1e"] .v-text-field.v-input--disabled .v-field {
  background-color: #1a1a1a !important;
  border-color: #333 !important;
  opacity: 0.6;
}

.v-card[style*="background-color: #1e1e1e"] .v-text-field.v-input--disabled .v-field__input {
  color: #666666 !important;
}
