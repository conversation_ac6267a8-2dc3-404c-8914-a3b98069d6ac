<template>
  <div class="property-sync-demo">
    <h2>属性面板同步更新演示</h2>
    
    <div class="demo-section">
      <h3>问题描述</h3>
      <p>之前的问题：当在编辑区删除组件元素时，属性面板中的配置项没有同步删除</p>
    </div>

    <div class="demo-section">
      <h3>修复后的效果</h3>
      <div class="demo-grid">
        <div class="demo-card">
          <h4>编辑区操作</h4>
          <ul>
            <li>删除按钮 → 属性面板中按钮配置同步删除</li>
            <li>删除筛选字段 → 属性面板中筛选配置同步删除</li>
            <li>删除表格列 → 属性面板中列配置同步删除</li>
            <li>添加新元素 → 属性面板中同步显示新配置</li>
          </ul>
        </div>
        
        <div class="demo-card">
          <h4>属性面板响应</h4>
          <ul>
            <li>✅ 实时同步删除对应配置项</li>
            <li>✅ 实时同步添加新配置项</li>
            <li>✅ 实时同步更新配置值</li>
            <li>✅ 保持界面状态一致性</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>核心修复内容</h3>
      <div class="code-block">
        <pre><code>// 新增核心同步函数
function updatePropertyConfigListField(fieldKey, index, operation, value) {
  // 1. 查找组件属性配置中对应的列表字段
  // 2. 根据操作类型同步更新配置
  // 3. 处理模板表达式和直接数组两种格式
  // 4. 发送组件更新事件
}

// 在删除、添加、更新函数中调用同步更新
removeListItem() {
  // ... 删除组件元素
  updatePropertyConfigListField(fieldKey, index, 'remove')
  // ... 发送更新事件
}
</code></pre>
      </div>
    </div>

    <div class="demo-section">
      <h3>支持的操作类型</h3>
      <div class="operation-grid">
        <div class="operation-card">
          <h4>删除操作</h4>
          <p>删除编辑区元素时，同步删除属性配置中对应的配置项</p>
        </div>
        <div class="operation-card">
          <h4>添加操作</h4>
          <p>添加编辑区元素时，同步添加属性配置中对应的配置项</p>
        </div>
        <div class="operation-card">
          <h4>更新操作</h4>
          <p>更新编辑区元素时，同步更新属性配置中对应的配置项</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这是一个演示组件，展示属性面板同步更新的修复效果
</script>

<style scoped>
.property-sync-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.demo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 15px;
}

.demo-card {
  padding: 15px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.operation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.operation-card {
  padding: 15px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #4CAF50;
}

.code-block {
  background: #2d2d2d;
  color: #f8f8f2;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin-top: 15px;
}

.code-block pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
}

h2 {
  color: #333;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 10px;
}

h3 {
  color: #555;
  margin-top: 0;
}

h4 {
  color: #666;
  margin-top: 0;
}

ul {
  margin: 10px 0;
  padding-left: 20px;
}

li {
  margin: 5px 0;
}
</style>
