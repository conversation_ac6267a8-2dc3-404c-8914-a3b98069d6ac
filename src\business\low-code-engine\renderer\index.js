import createRenderContext from './context';
import renderComponentFactory from './renderComponent'; // 导入工厂函数
import renderSlotFactory from './renderSlot';

// 在这里定义实际的 renderComponent 和 renderSlot 函数，解决循环引用
let renderComponent;
let renderSlot;

// 这里需要一个闭包来捕获 renderComponent 和 renderSlot 的最终值
const setupRenderers = (context) => {
  renderComponent = (componentMeta, currentContext) => {
    // 确保渲染函数被正确注入到当前上下文
    const contextWithRenderers = {
      ...currentContext,
      renderSlotFn: renderSlot,
      renderComponentFn: renderComponent
    };
    return renderComponentFactory(componentMeta, contextWithRenderers);
  };
  
  renderSlot = (slotContentMeta, currentContext) => {
    // 确保渲染函数被正确注入到当前上下文
    const contextWithRenderers = {
      ...currentContext,
      renderComponentFn: renderComponent,
      renderSlotFn: renderSlot
    };
    return renderSlotFactory(slotContentMeta, contextWithRenderers);
  };

  // 将最终确定的渲染函数注入到上下文（用于渲染根组件时）
  context.renderComponentFn = renderComponent;
  context.renderSlotFn = renderSlot;
};


/**
 * 渲染低代码页面主入口
 * @param {Object} meta - 元数据（包含components、data、methods、apis等）
 * @returns {Array<import('vue').VNode> | null} 渲染后的VNode树
 */
export function render(meta) {
  if (!meta || !Array.isArray(meta.components)) {
    console.warn('meta.components 必须为数组');
    return [];
  }

  // 1. 初始化页面级响应式数据（假设传入的meta.data已经是响应式对象或可以转换为响应式）
  // 真实场景可能需要 Vue 的 reactive/ref 来处理响应性
  const pageData = meta.data || {};
  const pageMethods = meta.methods || {};
  const pageApis = meta.apis || []; // API 定义

  // 2. 创建初始上下文
  const initialContext = createRenderContext(pageData, pageMethods, pageApis, meta.globalSlots, null);

  // 3. 设置渲染器，解决循环依赖
  setupRenderers(initialContext);

  // 4. 处理 lifecycle - onMounted
  if (meta.lifecycle?.onMounted && !initialContext._lifecycleMounted) {
    initialContext._lifecycleMounted = true; // 防止重复调用
    // 使用 Vue 的 nextTick 确保 DOM 渲染完成后执行
    import('vue').then(({ nextTick }) => {
      nextTick(() => {
        meta.lifecycle.onMounted.forEach(action => {
          if (action.action && typeof initialContext[action.action] === 'function') {
            try {
              initialContext[action.action](...(action.params || []));
            } catch (error) {
              console.error(`执行 lifecycle.onMounted.${action.action} 时发生错误:`, error);
            }
          } else {
            console.warn(`lifecycle.onMounted 中的方法 ${action.action} 不存在`);
          }
        });
      });
    });
  }

  // 渲染根组件树，过滤掉null值
  return meta.components
    .map(componentMeta => renderComponent(componentMeta, initialContext))
    .filter(vnode => vnode !== null && vnode !== undefined);
}

// 默认导出
export default {
  render
};