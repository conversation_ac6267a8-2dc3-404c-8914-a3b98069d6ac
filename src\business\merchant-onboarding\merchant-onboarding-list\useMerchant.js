export const useMerchant = () => {
  const merchantList = ref([]);

  const loadMerchantList = async (name) => {
    const res = await $api("/api/merchant-onboard/v1/search", {
      method: "POST",
      data: {
        merchantOnboardIds: null,
        statusCodes: null,
        size: -1,
      },
    });

    merchantList.value = res?.data?.records?.map((item) => {
      return {
        ...item,
        title: item.name,
        value: item.merchantOnboardId,
      };
    });
  };

  onMounted(() => {
    loadMerchantList();
  });

  return { merchantList, loadMerchantList };
};
