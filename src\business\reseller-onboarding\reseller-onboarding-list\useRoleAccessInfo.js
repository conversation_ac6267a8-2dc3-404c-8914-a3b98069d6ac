export const useRoleAccessInfo = (menuId) => {
  const roleAccessList = ref([]);

  const loadRoleAccessList = async () => {
    try {
      const res = await $api(
        `/api/admin-api/v1/user-profile/menu/role-access?menuId=${menuId}`,
        {
          method: "GET",
        }
      );

      roleAccessList.value = res?.data;
    } catch (error) {
      console.error("Faile to load role and access data:", error);
    }
  };

  onMounted(() => {
    loadRoleAccessList();
  });

  return { roleAccessList, loadRoleAccessList };
};
