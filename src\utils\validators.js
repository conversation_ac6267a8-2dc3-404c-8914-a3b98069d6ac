import { isEmpty, isEmptyArray, isNullOrUndefined } from './helpers'

// 👉 Required Validator
export const requiredValidator = value => {
  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'
  
  return !!String(value).trim().length || 'This field is required'
}

// 👉 Email Validator
export const emailValidator = value => {
  // limit 350
  if (String(value).length > 350)
    return 'The Email field length must be less than 350 characters'
  const re = /^(?:[^<>()[\]\\.,;:\s@"]+(?:\.[^<>()[\]\\.,;:\s@"]+)*|".+")@(?:\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\]|(?:[a-z\-\d]+\.)+[a-z]{2,})$/i
  if (Array.isArray(value))
    return value.every(val => re.test(String(val))) || 'The Email field must be a valid email'
  
  return re.test(String(value)) || 'The Email field must be a valid email'
}

// 👉 Password Validator
export const passwordValidator = password => {
  const regExp = /(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%&*()]).{8,}/
  const validPassword = regExp.test(password)
  
  return validPassword || 'Field must contain at least one uppercase, lowercase, special character and digit with min 8 chars'
}

// 👉 Confirm Password Validator
export const confirmedValidator = (value, target) => value === target || 'The Confirm Password field confirmation does not match'

// 👉 Between Validator
export const betweenValidator = (value, min, max) => {
  const valueAsNumber = Number(value)
  
  return (Number(min) <= valueAsNumber && Number(max) >= valueAsNumber) || `Enter number between ${min} and ${max}`
}

// 👉 Commission Rate Validator
export const commissionRateValidator = (value) => {
  // If decimal, maximum 2 decimal places
  const reg = /^\d+(\.\d{1,2})?$/

  const matches = String(value).match(reg)
   
  if (!matches) {
    return 'Please enter a number with up to 2 decimal places'
  }

  const num = parseFloat(matches[0])
  return num > 0 && num <= 100 || 'The Commission rate field must be between 0 and 100'
}

// 👉 Integer Validator
export const integerValidator = value => {
  if (isEmpty(value))
    return true
  if (Array.isArray(value))
    return value.every(val => /^-?\d+$/.test(String(val))) || 'This field must be an integer'
  
  return /^-?\d+$/.test(String(value)) || 'This field must be an integer'
}

// 👉 Regex Validator
export const regexValidator = (value, regex) => {
  if (isEmpty(value))
    return true
  let regeX = regex
  if (typeof regeX === 'string')
    regeX = new RegExp(regeX)
  if (Array.isArray(value))
    return value.every(val => regexValidator(val, regeX))
  
  return regeX.test(String(value)) || 'The Regex field format is invalid'
}

// 👉 Alpha Validator
export const alphaValidator = value => {
  if (isEmpty(value))
    return true
  
  return /^[A-Z]*$/i.test(String(value)) || 'The Alpha field may only contain alphabetic characters'
}

// 👉 URL Validator
export const urlValidator = value => {
  if (isEmpty(value))
    return true
  const re = /^https?:\/\/[^\s$.?#].\S*$/
  
  return re.test(String(value)) || 'URL is invalid'
}

// 👉 Length Validator
export const lengthValidator = (value, length) => {
  if (isEmpty(value))
    return true
  
  return String(value).length === length || `"The length of the Characters field must be ${length} characters."`
}

// 👉 Alpha-dash Validator
export const alphaDashValidator = value => {
  if (isEmpty(value))
    return true
  const valueAsString = String(value)
  
  return /^[\w-]*$/.test(valueAsString) || 'All Character are not valid'
}


// 👉 User group name Validator
export const userGroupNameValidator = value => {
  // limit 100
  if (String(value).length > 100)
    return 'The User group name field length must be less than 100 characters'
}


// 👉 User group description Validator
export const userGroupDescriptionValidator = value => {
  // limit 250
  if (String(value).length > 250)
    return 'The User group description field length must be less than 250 characters'
}

// 👉 Merchant onboarding list reject/approve remark Validator
export const merchantOnboardingListRemarkValidator = value => {
  if (isEmpty(value))
    return true
  // limit 250
  if (String(value).length > 250){
    return 'The remarks field length must be less than 250 characters'
  } else {
    return true
  }
    
}
// 👉 Merchant onboarding list review remark Validator
export const merchantOnboardingListReviewRemarkValidator = value => {
  if (isEmpty(value))
    return true
  // limit 500
  if (String(value).length > 500){
    return 'The remarks field length must be less than 500 characters'
  } else {
    return true
  }
    
}
// 👉 Create Merchant name Validator
export const merchantNameValidator = value => {
  if (isEmpty(value))
    return true
  if (String(value).trim().length < 6){
    return 'The Merchant Name field length must be more than 5 characters'
  } else if (String(value).trim().length > 200){
    return 'The Merchant Name field length must be less than 200 characters'
  } else {
    return true
  }
}
// 👉 Merchant Email Validator
export const merchanEmailValidator = value => {
  if (isEmpty(value))
    return true

  const emailTemp = value.trim()
  // limit 200
  if (String(emailTemp).length > 200)
    return 'The Merchant Email field length must be less than 200 characters'
  const re = /^(?:[^<>()[\]\\.,;:\s@"]+(?:\.[^<>()[\]\\.,;:\s@"]+)*|".+")@(?:\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\]|(?:[a-z\-\d]+\.)+[a-z]{2,})$/i
  if (Array.isArray(emailTemp))
    return emailTemp.every(val => re.test(String(val))) || 'The Merchant Email field must be a valid email'
  
  return re.test(String(emailTemp)) || 'The Merchant Email field must be a valid email'
}

// 👉 Length Limitation Validator
export const lengthLimitationValidator = (value, length, fieldName) => {
  if (isEmpty(value))
    return true
  // limit
  if (String(value).length > length){
    return `The ${fieldName} field length must be less than ${length} characters`
  } else {
    return true
  }
}

// 👉 Contact Number Validator
export const contactNumberValidator = (value) => {
  if (isEmpty(value))
    return true
  
  return (/^[\d\s-]+$/.test(String(value))) || "Please enter numbers only"
}