import * as VuetifyComponents from 'vuetify/components';

import { defineAsyncComponent } from 'vue';
import { reactive } from 'vue'

// 手动管理的自定义组件映射，避免循环引用
// 只导入确定不会产生循环引用的组件
export const dynamicCustomComponentMap = {};

// 预加载常用组件
async function preloadCommonComponents() {
  try {
    // 预加载 FilterPanel
    const filterPanel = await import('@/components/FilterPanel.vue');
    registerComponent('FilterPanel', filterPanel.default);

    // 预加载 TablePagination
    const tablePagination = await import('@/components/TablePagination.vue');
    registerComponent('TablePagination', tablePagination.default);

    // 预加载 TablePaginationLowCode
    const tablePaginationLowCode = await import('@/components/TablePaginationLowCode.vue');
    registerComponent('TablePaginationLowCode', tablePaginationLowCode.default);

    // 预加载 App 组件
    const appTextField = await import('@/components/AppTextField.vue');
    registerComponent('AppTextField', appTextField.default);

    const appSelect = await import('@/components/AppSelect.vue');
    registerComponent('AppSelect', appSelect.default);

    const appAutocomplete = await import('@/components/AppAutocomplete.vue');
    registerComponent('AppAutocomplete', appAutocomplete.default);

    const appDateTimePicker = await import('@/components/AppDateTimePicker.vue');
    registerComponent('AppDateTimePicker', appDateTimePicker.default);

    const appTextarea = await import('@/components/AppTextarea.vue');
    registerComponent('AppTextarea', appTextarea.default);

    console.log('常用组件预加载完成');
  } catch (error) {
    console.error('预加载组件失败:', error);
  }
}

// 立即执行预加载
preloadCommonComponents();

// 添加自定义组件的安全方法
export function registerComponent(name, component) {
  dynamicCustomComponentMap[name] = component;
}

// 延迟加载自定义组件的函数（用于需要时加载）
export async function loadCustomComponent(componentName) {
  // 如果已经注册，直接返回
  if (dynamicCustomComponentMap[componentName]) {
    return dynamicCustomComponentMap[componentName];
  }
  
  // 这里可以添加动态导入逻辑，但要小心循环引用
  try {
    switch (componentName) {
      case 'TablePagination':
        const tablePagination = await import('@/components/TablePagination.vue');
        const tablePaginationComponent = tablePagination.default;
        registerComponent(componentName, tablePaginationComponent);
        return tablePaginationComponent;

      case 'FilterPanel':
        const filterPanel = await import('@/components/FilterPanel.vue');
        const filterPanelComponent = filterPanel.default;
        registerComponent(componentName, filterPanelComponent);
        return filterPanelComponent;

      case 'TablePaginationLowCode':
        const tablePaginationLowCode = await import('@/components/TablePaginationLowCode.vue');
        const tablePaginationLowCodeComponent = tablePaginationLowCode.default;
        registerComponent(componentName, tablePaginationLowCodeComponent);
        return tablePaginationLowCodeComponent;

      // 可以添加更多组件的安全导入
      default:
        console.warn(`Unknown component: ${componentName}`);
        return null;
    }
  } catch (error) {
    console.error(`Failed to load component ${componentName}:`, error);
    return null;
  }
}

/**
 * 解析表达式，支持 {{ expression }} 语法
 * @param {string | any} expression - 待解析的表达式字符串或原始值
 * @param {Object} context - 完整的上下文对象
 * @returns {any} 解析后的值
 */
// 处理响应式对象 - 如果是 Vue 的 proxy，尝试获取原始值
const unProxy = (obj) => {
  if (obj && typeof obj === 'object') {
    // 方法1: 检查是否有 __v_raw 属性（Vue 3 响应式对象的内部属性）
    if (obj.__v_raw) {
      return obj.__v_raw;
    }
    
    // 方法2: 尝试通过 JSON 序列化和反序列化来"解除代理"
    try {
      const jsonStr = JSON.stringify(obj);
      const plainObj = JSON.parse(jsonStr);
      return plainObj;
    } catch (e) {
      // JSON 转换失败，继续下一种方法
    }
    
    // 方法3: 手动复制所有可枚举属性
    try {
      const plainObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          plainObj[key] = obj[key];
        }
      }
      return plainObj;
    } catch (e) {
      // 手动复制失败，返回原对象
    }
    
    return obj;
  }
  return obj;
};

// 解析单个表达式的辅助函数
const resolveSingleExpression = (expr, context) => {
  try {
    // 🔥 修复：先尝试直接从context获取数据
    if (expr && context && context[expr]) {
      console.log(`直接从context获取 ${expr}:`, context[expr])
      return context[expr]
    }
    
    // 创建一个扁平的数据上下文，包含所有必要的数据
    let flatContext = {
      ...context,
      ...(context.data || {}),
      // 确保插槽参数能够被访问
      ...(context.slotProps || {})
    };
    
    // 如果有 item 对象，尝试解除 proxy
    if (flatContext.item) {
      const rawItem = unProxy(flatContext.item);
      flatContext.item = rawItem;
      flatContext = { ...flatContext, ...rawItem }; // 也将属性扁平化到顶层
    }
    
    console.log(`表达式解析: ${expr}, flatContext包含:`, Object.keys(flatContext))
    
    // 使用 new Function 来解析表达式，注意安全性
    const code = `with(flatContext) { return ${expr} }`;
    const result = new Function('flatContext', code)(flatContext);
    
    console.log(`表达式 ${expr} 解析结果:`, result)
    return result;
  } catch (e) {
    console.error('表达式解析错误:', expr, e);
    console.log('当前context:', context);
    
    // 🔥 修复：更智能的兜底处理
    if (expr === 'headers' && context?.headers) {
      console.log('使用兜底值 - headers:', context.headers)
      return context.headers
    }
    if (expr === 'listData' && context?.listData) {
      return context.listData
    }
    if (expr === 'totalCount' && context?.totalCount !== undefined) {
      return context.totalCount
    }
    if (expr === 'isLoading' && context?.isLoading !== undefined) {
      return context.isLoading
    }
    
    // 如果是item表达式，尝试手动解析
    if (expr.includes('item.') && context.item) {
      try {
        const parts = expr.split('.');
        if (parts[0] === 'item' && parts.length === 2) {
          const item = unProxy(context.item);
          const result = item[parts[1]];
          return result;
        }
      } catch (manualError) {
        console.error('手动解析也失败:', manualError);
      }
    }
    
    console.warn(`表达式 ${expr} 解析完全失败，返回空值`)
    return undefined; // 🔥 修复：返回undefined而不是原始表达式
  }
};

// 处理混合文本：静态文本 + 动态表达式
const processMixedText = (text, context) => {
  let result = text;
  const regex = /\{\{([^}]+)\}\}/g;
  let match;
  
  while ((match = regex.exec(text)) !== null) {
    const fullMatch = match[0]; // 完整的 {{...}}
    const expr = match[1].trim(); // 提取的表达式
    
    try {
      // 解析单个表达式
      const resolvedValue = resolveSingleExpression(expr, context);
      // 替换原文本中的表达式
      result = result.replace(fullMatch, resolvedValue);
    } catch (e) {
      console.error('混合文本表达式片段解析失败:', expr, e);
      // 解析失败时保留原始表达式
    }
  }
  
  return result;
};

export const resolveExpression = (expression, context) => {
  if (typeof expression !== 'string') return expression;
  
  // 处理混合文本：检查字符串中是否包含 {{...}} 表达式
  if (expression.includes('{{') && expression.includes('}}')) {
    // 如果不是纯表达式，则处理为混合文本
    if (!(expression.startsWith('{{') && expression.endsWith('}}'))) {
      return processMixedText(expression, context);
    }
  }
  
  // 处理纯表达式
  const match = expression.match(/^\{\{(.*)\}\}$/);
  const expr = match ? match[1].trim() : null;

  if (!expr) {
      // 尝试解析单花括号（例如在 v-for 的 :key 中）
      if (expression.startsWith('{') && expression.endsWith('}')) {
          const singleBraceExpr = expression.substring(1, expression.length - 1).trim();
          if (singleBraceExpr) {
              return resolveSingleExpression(singleBraceExpr, context);
          }
      }
      return expression;
  }
  
  return resolveSingleExpression(expr, context);
};

/**
 * 根据组件名获取实际的Vue组件
 * @param {string} name - 组件名
 * @param {Object} context - 渲染上下文，包含 appContext
 * @returns {import('vue').Component | string} Vue组件对象或组件名字符串
 */
export const getComponentByName = (name, context) => {
  if (isHtmlTag(name)) {
    return name
  }

  // 1. 首先尝试从手动注册的自定义组件映射中查找
  if (dynamicCustomComponentMap[name]) {
    return dynamicCustomComponentMap[name];
  }

  // 2. 尝试从Vue应用程序上下文中获取已注册的全局组件 (例如通过 app.component 注册的组件)
  if (context && context.appContext && context.appContext.app) {
    const globalComponent = context.appContext.app.component(name);
    if (globalComponent) {
      return globalComponent;
    }
  }

  // 3. 尝试从Vuetify的组件集合中查找
  if (VuetifyComponents[name]) {
    return VuetifyComponents[name];
  }

  // 4. 如果组件未找到，尝试异步加载
  if (['FilterPanel', 'TablePagination', 'TablePaginationLowCode'].includes(name)) {
    // 如果预加载失败，尝试动态加载
    loadCustomComponent(name).then(component => {
      if (component) {
        console.log(`组件 ${name} 动态加载成功`);
      }
    });
  }

  // 5. 最后，返回原始名称，Vue会将其视为原生HTML标签
  console.warn(`组件 ${name} 未找到，使用原始名称`);
  return name;
};

export const isHtmlTag = (name) => {
  const htmlTags = ['div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a', 'img', 'button', 'input', 'textarea', 'select', 'option', 'label', 'form', 'table', 'tr', 'td', 'th', 'tbody', 'thead', 'tfoot', 'ul', 'li', 'ol', 'dl', 'dt', 'dd', 'blockquote', 'code', 'pre', 'hr', 'br', 'meta', 'link', 'script', 'style', 'title', 'head', 'body', 'html'];
  return htmlTags.includes(name);
}

/**
 * 从JSON配置中提取所有使用的变量
 * @param {Object} config - 页面配置对象
 * @returns {Set} - 使用的变量名集合
 */
export function extractUsedVariables(config) {
  const variables = new Set()
  
  // 递归遍历对象，查找所有{{}}表达式
  function traverse(obj) {
    if (typeof obj === 'string') {
      // 匹配 {{variable}} 或 {{object.property}} 格式
      const matches = obj.match(/\{\{([^}]+)\}\}/g)
      if (matches) {
        matches.forEach(match => {
          // 提取变量名，去掉 {{ 和 }}
          const expr = match.slice(2, -2).trim()
          // 如果是对象属性，只取根对象名
          const rootVar = expr.split('.')[0].split('[')[0]
          variables.add(rootVar)
        })
      }
    } else if (Array.isArray(obj)) {
      obj.forEach(item => traverse(item))
    } else if (obj && typeof obj === 'object') {
      Object.values(obj).forEach(value => traverse(value))
    }
  }
  
  traverse(config)
  return variables
}

/**
 * 根据使用的变量创建上下文对象
 * @param {Set} variables - 变量名集合
 * @param {Object} data - 实际数据
 * @returns {Object} - 上下文对象
 */


export function createRenderContext(variables, data = {}) {
  const context = {}

  // 为每个变量设置默认值
  const defaultValues = {
    // 通用数据类型
    headers: [],
    listData: [],
    accounts: [],
    items: [],
    rows: [],

    // 计数相关
    totalCount: 0,
    totalUsers: 0,
    totalItems: 0,
    count: 0,
    total: 0,

    // 状态相关
    isLoading: false,
    loading: false,
    submitting: false,

    // 选择相关
    selectedIds: [],
    selectedRows: [],
    selected: [],

    // 分页相关
    page: 1,
    itemsPerPage: 10,
    pageSize: 10,

    // 表单相关
    formData: {},
    searchQuery: {},
    filters: {},

    // UI状态
    expanded: [],
    visible: true,
    disabled: false,
  }

  variables.forEach(varName => {
    if (data.hasOwnProperty(varName)) {
      // 使用实际数据，但确保formData是响应式的
      if (varName === 'formData' && data[varName]) {
        // 确保formData是响应式的，并且有默认的status字段
        context[varName] = reactive({
          status: true, // 默认状态为true
          ...data[varName]
        })
        console.log('createRenderContext: 设置响应式formData为:', context[varName])
      } else {
        context[varName] = data[varName]
      }
    } else if (defaultValues.hasOwnProperty(varName)) {
      // 使用默认值，特殊处理formData
      if (varName === 'formData') {
        context[varName] = reactive({ status: true }) // 确保有默认的status字段且是响应式的
        console.log('createRenderContext: 使用默认响应式formData:', context[varName])
      } else {
        context[varName] = defaultValues[varName]
      }
    } else {
      // 根据变量名推测类型
      if (varName.includes('Count') || varName.includes('total') || varName.includes('Total')) {
        context[varName] = 0
      } else if (varName.includes('loading') || varName.includes('Loading') || varName.startsWith('is')) {
        context[varName] = false
      } else if (varName.includes('Data') || varName.includes('List') || varName.includes('Items')) {
        context[varName] = []
      } else {
        context[varName] = undefined
      }
    }
  })

  return context
}

