import {
  PAGINATION_COMPONENTS,
  createPaginationComponent,
} from "./pagination.js";
import {
  generateTableHeaders,
  mapFieldToComponent,
} from "./componentMapping.js";

/**
 * Page Generator Class
 * Automatically generates frontend page configurations based on backend metadata
 */
export class PageGenerator {
  constructor(metaConfig, options = {}) {
    this.metaConfig = metaConfig;
    this.scenario = metaConfig.scenario;
    this.fields = metaConfig.fields;
    this.apis = metaConfig.apis;
    this.options = {
      paginationComponent: PAGINATION_COMPONENTS.TABLE_PAGINATION, // 默认使用TablePagination
      paginationConfig: {}, // 分页组件的额外配置
      ...options,
    };
    this.idCounter = 0;
  }

  generateId(prefix = "comp") {
    return `${prefix}-${this.scenario}-${Date.now()}-${++this.idCounter}`;
  }

  /**
   * Generates the list page configuration.
   * @returns {Object} The complete JSON configuration for the list page.
   */
  generateListPage() {
    console.log("PageGenerator: 开始生成列表页");
    console.log("PageGenerator: this.fields:", this.fields);
    console.log("PageGenerator: this.metaConfig:", this.metaConfig);

    const searchFields = this.fields.filter((field) => field.queryable);
    console.log("PageGenerator: searchFields:", searchFields);

    const tableHeaders = generateTableHeaders(this.fields);
    console.log("PageGenerator: tableHeaders:", tableHeaders);

    const result = {
      id: `${this.scenario}-list`,
      module: `${this.metaConfig.displayName || this.scenario}`,
      name: `${this.metaConfig.displayName || this.scenario}`,
      path: `/low-code/${this.scenario.toLowerCase()}/list`,
      layout: "default",

      lifecycle: {
        onMounted: [{ action: "initData" }, { action: "handleSearch" }],
      },

      data: {
        // Search query parameters
        searchQuery: this._generateSearchQueryData(searchFields),

        // List data
        isLoading: false,
        listData: [],
        totalCount: 0,
        page: 1,
        itemsPerPage: 10,

        // Selection related
        selectedRows: [],
        selectedIds: [],

        // Table configuration
        headers: tableHeaders,
        paginationOptions: [10, 20, 50, 100],
      },

      components: [
        {
          id: this.generateId("title-bar"),
          componentName: "TitleBar",
          type: "VCard",
          props: { ripple: false },
          children: [
            // Title bar
            this._generateTitleSection(),
          ],
          // 属性面板配置
          propertyConfig: {
            sections: [
              {
                key: "title",
                title: "Title Settings",
                icon: "mdi-format-title",
                fields: [
                  {
                    key: "title",
                    label: "Title Text",
                    type: "text",
                    placeholder: "Enter Title",
                    defaultValue: this.metaConfig.displayName || this.scenario,
                  },
                ],
              },
              {
                key: "buttons",
                title: "Right Buttons",
                icon: "mdi-button-cursor",
                fields: [
                  {
                    key: "buttons",
                    label: "Button List",
                    type: "list",
                    addable: true,
                    removable: true,
                    itemFields: [
                      { key: "text", label: "Button Text", type: "text" },
                      {
                        key: "variant",
                        label: "Button Style",
                        type: "select",
                        options: [
                          { title: "Flat", value: "flat" },
                          { title: "Outlined", value: "outlined" },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
        // 🔥 修复：搜索区域独立的Card（如果有搜索字段）
        ...(searchFields.length > 0
          ? [
              {
                id: this.generateId("filter-panel"),
                componentName: "FilterPanel",
                type: "VCard",
                props: { ripple: false },
                children: [this._generateSearchSection(searchFields, true)],
                // 属性面板配置
                propertyConfig: {
                  sections: [
                    {
                      key: "filters",
                      title: "Filter Fields",
                      icon: "mdi-filter-variant",
                      fields: [
                        {
                          key: "filters",
                          label: "Filter Fields",
                          type: "list",
                          addable: true,
                          removable: true,
                          itemFields: [
                            { key: "field", label: "Field Name", type: "text" },
                            {
                              key: "label",
                              label: "Display Label",
                              type: "text",
                            },
                            {
                              key: "type",
                              label: "Field Type",
                              type: "text",
                              readonly: true,
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ]
          : []),
        // 🔥 修复：表格区域独立的Card
        {
          id: this.generateId("data-table"),
          componentName: "DataTable",
          type: "VCard",
          props: { ripple: false },
          children: [
            // Batch operations area
            this._generateBatchActionsSection(),

            // Data table with custom pagination
            this._generateDataTableSection(),
          ],
          // 属性面板配置
          propertyConfig: {
            sections: [
              {
                key: "columns",
                title: "Table Columns",
                icon: "mdi-table-column",
                fields: [
                  {
                    key: "columns",
                    label: "Column Configuration",
                    type: "list",
                    addable: true,
                    removable: true,
                    itemFields: [
                      { key: "key", label: "Field Name", type: "text" },
                      { key: "title", label: "Column Title", type: "text" },
                      { key: "sortable", label: "Sortable", type: "switch" },
                    ],
                  },
                ],
              },
            ],
          },
        },
      ],

      apis: this._generateListApis(),
      methods: this._generateListMethods(),
    };

    console.log("PageGenerator: 列表页配置生成完成:", result);
    return result;
  }

  /**
   * Generates the form page configuration.
   * @param {string} mode - Form mode: 'create' | 'edit' | 'detail'
   * @returns {Object} The complete JSON configuration for the form page.
   */
  generateFormPage(mode = "create") {
    const formFields = this.fields.filter(
      (field) =>
        !field.system &&
        (field.layout.component !== "PASSWORD" || mode === "create")
    );

    return {
      id: `${this.scenario}-${mode}`,
      module: `${this.metaConfig.displayName || this.scenario}`,
      name: `${mode === "create" ? "New" : mode === "edit" ? "Edit" : "View"} ${
        this.metaConfig.displayName || this.scenario
      }`,
      path: `/low-code/${this.scenario.toLowerCase()}/${mode}`,
      title: `${
        mode === "create" ? "New" : mode === "edit" ? "Edit" : "View"
      } ${this.metaConfig.displayName || this.scenario}`,
      layout: "default",

      lifecycle: {
        onMounted: [
          { action: "initFormData" },
          ...(mode !== "create" ? [{ action: "loadDetailData" }] : []),
        ],
      },

      data: {
        // Form data
        formData: this._generateFormData(),

        // Page state
        isLoading: false,
        isSubmitting: false,

        // Mode identifier
        mode: mode,
        entityId: mode !== "create" ? "{{$route.query.id}}" : null,
      },

      components: [
        // 第一个组件：标题区域
        {
          id: this.generateId("form-title"),
          type: "VCard",
          props: { ripple: false },
          children: [this._generateFormTitleSection(mode)],
          // 组件数据
          data: {
            title: `${
              mode === "create" ? "New" : mode === "edit" ? "Edit" : "View"
            } ${this.metaConfig.displayName || this.scenario}`,
          },
          // 标题卡片属性配置
          propertyConfig: {
            sections: [
              {
                key: "title",
                title: "Title Settings",
                icon: "mdi-format-title",
                fields: [
                  {
                    key: "title",
                    label: "Title Text",
                    type: "text",
                    placeholder: "Enter title text",
                    defaultValue: "{{title}}",
                  },
                ],
              },
            ],
          },
        },
        // 第二个组件：内容区域
        {
          id: this.generateId("form-content"),
          type: "VCard",
          props: {
            style: { backgroundColor: "#1e1e1e" },
            ripple: false,
          },
          children: [this._generateFormContentSection(formFields, mode)],
          // 组件数据
          data: {
            fields: this.fields
              .filter((field) => !field.system)
              .map((field) => ({
                key: field.name,
                label: field.label || field.name,
                required: field.required || false,
                placeholder: field.placeholder || "",
                component: field.layout?.component || "VTextField",
              })),
          },
          // 内容卡片属性配置
          propertyConfig: {
            sections: [
              {
                key: "fields",
                title: "Form Fields",
                icon: "mdi-form-textbox",
                fields: [
                  {
                    key: "fields",
                    label: "Form Fields",
                    type: "list",
                    addable: false,
                    removable: true,
                    itemFields: [
                      {
                        key: "key",
                        label: "Field Key",
                        type: "text",
                        readonly: true,
                      },
                      {
                        key: "label",
                        label: "Field Label",
                        type: "text",
                        readonly: true,
                      },
                      {
                        key: "required",
                        label: "Required",
                        type: "switch",
                        readonly: true,
                      },
                      {
                        key: "component",
                        label: "Component Type",
                        type: "text",
                        readonly: true,
                      },
                    ],
                    defaultValue: "{{fields}}",
                  },
                ],
              },
            ],
          },
        },
        // 第三个组件：操作按钮区域
        {
          id: this.generateId("form-actions"),
          type: "VCard",
          props: { ripple: false },
          children: [this._generateFormActionsSection(mode)],
          // 组件数据
          data: {
            buttons:
              mode === "detail"
                ? [{ text: "Back", variant: "flat", color: "primary" }]
                : [
                    { text: "Save", variant: "flat", color: "primary" },
                    { text: "Cancel", variant: "outlined", color: "secondary" },
                  ],
          },
          // 操作按钮卡片属性配置
          propertyConfig: {
            sections: [
              {
                key: "buttons",
                title: "Action Buttons",
                icon: "mdi-button-cursor",
                fields: [
                  {
                    key: "buttons",
                    label: "Buttons",
                    type: "list",
                    addable: false,
                    removable: false,
                    itemFields: [
                      { key: "text", label: "Button Text", type: "text" },
                      {
                        key: "variant",
                        label: "Button Variant",
                        type: "select",
                        options: ["flat", "outlined", "elevated"],
                      },
                      {
                        key: "color",
                        label: "Button Color",
                        type: "select",
                        options: [
                          "primary",
                          "secondary",
                          "success",
                          "error",
                          "warning",
                          "info",
                        ],
                      },
                    ],
                    defaultValue: "{{buttons}}",
                  },
                ],
              },
            ],
          },
        },
      ],

      apis: this._generateFormApis(mode),
      methods: this._generateFormMethods(mode),
    };
  }

  // ===== Private Methods - Data Generation =====

  _generateSearchQueryData(searchFields) {
    const searchQuery = {};
    searchFields.forEach((field) => {
      searchQuery[field.name] = null;
    });

    return searchQuery;
  }

  _generateFormData() {
    const formData = {};

    this.fields
      .filter((field) => !field.system)
      .forEach((field) => {
        switch (field.layout.component) {
          case "NUMBER":
            formData[field.name] = null;
            break;
          case "SWITCH":
            formData[field.name] = false;
            break;
          case "SELECT":
            formData[field.name] = null;
            break;
          default:
            formData[field.name] = "";
        }
      });

    return formData;
  }

  // ===== Private Methods - Component Generation =====

  _generateTitleSection() {
    return {
      type: "VCardItem",
      props: { class: "px-0 pb-4" },
      children: [
        {
          type: "VCardTitle",
          props: { class: "px-0" },
          children: [
            {
              type: "div",
              props: { class: "d-flex align-center flex-wrap" },
              children: [
                {
                  type: "div",
                  props: { text: this.metaConfig.displayName || this.scenario },
                },
                { type: "VSpacer" },
                {
                  type: "div",
                  props: { class: "d-flex align-center gap-4" },
                  children: [
                    {
                      type: "VBtn",
                      props: { text: "CREATE" },
                      events: {
                        click: [{ action: "handleCreate" }],
                      },
                      permission: ["Add", this.scenario],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  }

  _generateSearchSection(searchFields, hideLabels = false) {
    const searchCols = searchFields.map((field) => {
      const componentConfig = mapFieldToComponent(field, "search", {
        hideLabels,
      });

      return {
        type: "VCol",
        props: {
          cols: 12,
          sm: 4,
          xl: 3,
          xxl: 2,
        },
        children: [componentConfig],
      };
    });

    return {
      type: "FilterPanel",
      props: {
        hideLabels: hideLabels,
      },
      children: searchCols,
    };
  }

  _generateBatchActionsSection() {
    return {
      type: "VCardText",
      props: {
        class:
          "d-flex align-center gap-4 py-2 px-4 bg-grey-lighten-4 justify-start",
      },
      "v-if": "{{selectedRows.length > 0}}",
      children: [
        {
          type: "span",
          props: {
            class: "text-primary font-weight-medium",
            text: "{{selectedRows.length}}",
          },
        },
        {
          type: "span",
          props: { text: " items selected" },
        },
        {
          type: "VIcon",
          props: {
            size: 20,
            icon: "mdi-close-circle",
          },
          events: {
            click: [{ action: "clearAllSelected" }],
          },
        },
        {
          type: "VDivider",
          props: {
            vertical: true,
            color: "#999",
            class: "my-auto",
          },
        },
        {
          type: "VBtn",
          props: {
            variant: "tonal",
            color: "error",
            text: "Batch Delete",
          },
          events: {
            click: [{ action: "batchDelete" }],
          },
          permission: ["Delete", this.scenario],
        },
      ],
    };
  }

  _generateDataTableSection() {
    return {
      type: "div",
      children: [
        // 🔥 修复：数据表格（禁用内置分页）
        {
          type: "VDataTableServer",
          model: "{{selectedIds}}",
          props: {
            items: "{{listData}}",
            itemValue: "id",
            headers: "{{headers}}",
            loading: "{{isLoading}}",
            showSelect: true,
            class: "text-no-wrap",
            // 🔥 关键：禁用内置分页和底部信息
            hideDefaultFooter: true,
            itemsPerPage: -1,
          },
          events: {
            "click:row": [{ action: "handleRowClick" }],
            "update:sort-by": [{ action: "handleSortChange" }],
            "update:modelValue": [{ action: "handleSelectChange" }],
          },
          slots: this._generateTableSlots(),
        },
        // 🔥 修复：使用配置的自定义分页组件
        createPaginationComponent({
          component: this.options.paginationComponent,
          ...this.options.paginationConfig,
        }),
      ],
    };
  }

  _generateTableSlots() {
    const slots = {
      "item.no": {
        type: "span",
        props: { text: "{{item.no}}" },
      },
    };

    // Generate slots for each field
    this.fields.forEach((field) => {
      if (field.frontend?.showInList !== false) {
        const componentConfig = mapFieldToComponent(field, "list");
        if (componentConfig) {
          slots[`item.${field.name}`] = componentConfig;
        }
      }
    });

    // Action column slot
    slots["item.actions"] = {
      type: "div",
      props: { class: "d-flex gap-2" },
      children: [
        {
          type: "IconBtn",
          props: { "v-tooltip": "'View'" },
          events: {
            click: [{ action: "viewDetail", params: ["item"] }],
          },
          permission: ["View", this.scenario],
          children: [
            {
              type: "VIcon",
              props: {
                icon: "mdi-eye",
                class: "hover:text-primary",
              },
            },
          ],
        },
        {
          type: "IconBtn",
          props: { "v-tooltip": "'Edit'" },
          events: {
            click: [{ action: "editItem", params: ["item"] }],
          },
          permission: ["Edit", this.scenario],
          children: [
            {
              type: "VIcon",
              props: {
                icon: "mdi-pencil",
                class: "hover:text-primary",
              },
            },
          ],
        },
        {
          type: "IconBtn",
          props: { "v-tooltip": "'Delete'" },
          events: {
            click: [{ action: "deleteItem", params: ["item"] }],
          },
          permission: ["Delete", this.scenario],
          children: [
            {
              type: "VIcon",
              props: {
                icon: "mdi-trash-can",
                class: "hover:text-error",
              },
            },
          ],
        },
      ],
    };

    return slots;
  }

  _generateFormTitleSection(mode) {
    const titleText =
      mode === "create" ? "New" : mode === "edit" ? "Edit" : "View";

    return {
      type: "VCardItem",
      props: { class: "px-0 pb-4" },
      children: [
        {
          type: "VCardTitle",
          props: { class: "px-0" },
          children: [
            {
              type: "div",
              props: {
                class: "d-flex align-center gap-4",
                text: `${titleText} ${
                  this.metaConfig.displayName || this.scenario
                }`,
              },
            },
          ],
        },
      ],
    };
  }

  _generateFormContentSection(formFields, mode) {
    // 根据字段数量决定布局：≤5个字段单列，>5个字段双列
    const fieldCount = formFields.length;
    const useSingleColumn = fieldCount <= 5;

    const formCols = formFields.map((field) => {
      const componentConfig = mapFieldToComponent(field, "form");

      return {
        type: "VCol",

        props: {
          cols: 12,
          md: useSingleColumn ? 12 : 6, // 根据字段数量决定列宽
          style: {
            maxWidth: useSingleColumn ? "600px" : "450px",
          },
        },
        children: [componentConfig],
        // 属性面板配置
      };
    });

    return {
      type: "VCardText",
      props: { class: "px-0" },
      children: [
        {
          type: "div",
          props: {
            // 限制容器最大宽度
            style: {
              // maxWidth: useSingleColumn ? "600px" : "900px",
            },
            class: "px-0 py-2",
          },
          children: [
            {
              type: "VForm",
              props: {
                disabled: mode === "detail",
              },
              children: [
                {
                  type: "VRow",
                  props: {
                    class: "ma-0",
                  },
                  children: formCols,
                },
              ],
            },
          ],
        },
      ],
    };
  }

  _generateFormActionsSection(mode) {
    if (mode === "detail") {
      return {
        type: "VCardActions",
        props: { class: "px-0 pt-4 justify-end" },
        children: [
          {
            type: "VBtn",
            props: {
              text: "Back",
              variant: "flat",
              color: "primary",
              style: { width: "120px" },
            },
          },
        ],
      };
    }
    return {
      type: "VCardActions",
      props: { class: "flex px-0 pt-4 justify-end" },
      children: [
        {
          type: "VBtn",
          props: {
            color: "primary",
            variant: "flat",
            text: "Save",
            loading: "{{isSubmitting}}",
            style: { width: "120px" },
          },
          events: {
            click: [
              { action: mode === "create" ? "handleCreate" : "handleUpdate" },
            ],
          },
        },
        {
          type: "VBtn",
          props: {
            variant: "outlined",
            text: "Cancel",
            style: { width: "120px" },
          },
          events: {
            click: [{ action: "handleCancel" }],
          },
        },
      ],
    };
  }

  // ===== Private Methods - API Generation =====

  _generateListApis() {
    return [
      {
        id: "list-data",
        type: "http",
        method: "POST",
        url: "/api/admin-api/v1/config-center/items/page",
      },
    ];
  }

  _generateFormApis(mode) {
    const apis = [];

    if (mode !== "create") {
      apis.push({
        id: "get-detail",
        type: "http",
        method: "GET",
        url: "/api/admin-api/v1/config-center/items/get",
      });
    }

    if (mode === "create") {
      apis.push({
        id: "create-item",
        type: "http",
        method: "POST",
        url: "/api/admin-api/v1/config-center/items",
      });
    } else if (mode === "edit") {
      apis.push({
        id: "update-item",
        type: "http",
        method: "PUT",
        url: "/api/admin-api/v1/config-center/items",
      });
    }

    return apis;
  }

  // ===== Private Methods - Method Generation =====

  _generateListMethods() {
    return {
      initData: {
        type: "custom",
        script: "console.log('Initializing list data');",
      },

      handleSearch: {
        type: "custom",
        script: `
          this.setData('page', 1);
          const params = {
            scenario: '${this.scenario}',
            page: this.page,
            size: this.itemsPerPage,
            configData: this.data.searchQuery,
          }
          this.apiManager.callApi('list-data', params).then(res => {
            const dataWithNo = (res.data.records || []).map((item, idx) => ({
              ...item,
              ...(item.configData || {}),
              configData: undefined,
              no: (this.page - 1) * this.itemsPerPage + idx + 1
            }));
            this.setData('listData', dataWithNo);
            this.setData('totalCount', res.data.total || 0);
          }).catch(err => {
            console.error('API call failed:', err);
          });
        `,
      },

      handleCreate: {
        type: "navigate",
        to: `/low-code/${this.scenario.toLowerCase()}/create`,
      },

      editItem: {
        params: ["item"],
        type: "navigate",
        to: `/low-code/${this.scenario.toLowerCase()}/edit`,

        paramsMap: {
          id: "{{item.id}}",
        },
      },

      viewDetail: {
        params: ["item"],
        type: "navigate",
        to: `/low-code/${this.scenario.toLowerCase()}/detail`,
        paramsMap: {
          id: "{{item.id}}",
        },
      },

      deleteItem: {
        params: ["item"],
        type: "confirm",
        title: "Confirm Delete",
        text: "This operation will permanently delete the record, are you sure?",
        onConfirm: [
          {
            action: "callApi",
            apiId: "delete-item",
            params: { id: "{{item.id}}" },
            onSuccess: [
              {
                action: "message",
                messageType: "success",
                text: "Deleted successfully",
              },
              { action: "handleSearch" },
            ],
          },
        ],
      },

      batchDelete: {
        type: "confirm",
        title: "Confirm Batch Delete",
        text: "This operation will permanently delete the selected records, are you sure?",
        onConfirm: [
          {
            action: "callApi",
            apiId: "batch-delete",
            params: { ids: "{{selectedIds}}" },
            onSuccess: [
              {
                action: "message",
                messageType: "success",
                text: "Batch deleted successfully",
              },
              { action: "clearAllSelected" },
              { action: "handleSearch" },
            ],
          },
        ],
      },

      clearAllSelected: {
        type: "custom",
        script: `
          this.setData('selectedRows', []);
          this.setData('selectedIds', []);
        `,
      },

      handleSelectChange: {
        type: "custom",
        script: `
          const selection = arguments[0];
          const selectedItems = (this.data.listData || []).filter(item => 
            selection.includes(item.id)
          );
          this.setData('selectedRows', selectedItems);
          this.setData('selectedIds', selection);
        `,
      },

      handleSortChange: {
        type: "custom",
        script: `
          const options = arguments[0];
          this.setData('page', 1);
          this.handleSearch();
        `,
      },
    };
  }

  _generateFormMethods(mode) {
    const methods = {
      initFormData: {
        type: "custom",
        script: "console.log('Initializing form data');",
      },

      handleCancel: {
        type: "navigate",
        to: `/${this.scenario.toLowerCase()}/list`,
      },
    };

    if (mode !== "create") {
      methods.loadDetailData = {
        type: "custom",
        script: `
          const id = this.data.entityId;
          if (id) {
            const params = {
              scenario: '${this.scenario}',
              configItemId: id,
            }
            this.setData('isLoading', true);
            this.apiManager.callApi('get-detail', params).then(res => {
              this.setData('formData', res.data);
            }).catch(err => {
              console.error('Failed to load detail:', err);
            }).finally(() => {
              this.setData('isLoading', false);
            });
          }
        `,
      };
    }

    if (mode === "create") {
      methods.handleCreate = {
        type: "custom",
        script: `
          this.setData('isSubmitting', true);
          const params = {
            scenario: '${this.scenario}',
            configData: {
              ...this.data.formData,
            }
          }
          this.apiManager.callApi('create-item', params).then(res => {
            this.message('success', 'Created successfully');
            this.navigateTo('/${this.scenario.toLowerCase()}/list');
          }).catch(err => {
            console.error('Failed to create:', err);
          }).finally(() => {
            this.setData('isSubmitting', false);
          });
        `,
      };
    } else if (mode === "edit") {
      methods.handleUpdate = {
        type: "custom",
        script: `
          this.setData('isSubmitting', true);
          this.apiManager.callApi('update-item', { 
            id: this.data.entityId, 
            scenario: '${this.scenario}',
            version: 0,
            configData: {
              ...this.data.formData,
            }
          }).then(res => {
            this.message('success', 'Updated successfully');
            this.navigateTo('/${this.scenario.toLowerCase()}/list');
          }).catch(err => {
            console.error('Failed to update:', err);
          }).finally(() => {
            this.setData('isSubmitting', false);
          });
        `,
      };
    }

    // 添加表单数据更新方法
    methods.updateFormData = {
      type: "custom",
      script: `
        const fieldName = arguments[0];
        const newValue = arguments[1];
        console.log('updateFormData called with:', fieldName, newValue);
        if (fieldName && this.data.formData) {
          // 直接更新formData对象
          this.data.formData[fieldName] = newValue;
          console.log('Form data updated:', fieldName, '=', newValue);
          console.log('Current formData:', this.data.formData);
        } else {
          console.warn('updateFormData: missing fieldName or formData', { fieldName, formData: this.data.formData });
        }
      `,
    };

    return methods;
  }
}

/**
 * Convenience method: Generates complete page configurations from backend metadata.
 * @param {Object} backendMeta - Backend metadata.
 * @param {Object} options - Generation options, including paginationComponent
 * @returns {Object} An object containing all page configurations.
 */
export function generatePagesFromBackendMeta(backendMeta, options = {}) {
  // Extend backend metadata, add frontend configuration
  const enrichedMeta = {
    ...backendMeta,
    displayName: backendMeta.displayName || backendMeta.scenario.split(":")[1],
    apis: {
      baseUrl: `/api/meta/${backendMeta.scenario}`,
      ...backendMeta.apis,
    },
    fields: backendMeta.fields.map((field) => ({
      ...field,
      frontend: {
        showInList: true,
        sortable: true,
        columnWidth: "auto",
        ...field.frontend,
      },
    })),
  };

  const generator = new PageGenerator(enrichedMeta, options);

  return {
    list: generator.generateListPage(),
    create: generator.generateFormPage("create"),
    edit: generator.generateFormPage("edit"),
    detail: generator.generateFormPage("detail"),
  };
}
