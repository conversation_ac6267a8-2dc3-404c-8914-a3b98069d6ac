<script setup lang="ts">
import { treeToList } from '@/utils/helpers'
import { usePermissionList } from './usePermissionList'
import { useRouter, useRoute } from 'vue-router'


const tableData = defineModel({
  type: Object,
  required: true,
})

const route = useRoute();
const roleId = route.query.id as string;

const { permissionList } = usePermissionList(roleId)

const expandedRows = Array.from({ length: 99 }, (_, index) => index + 1) as any[];

const headers = [
  { title: 'Product Module & Feature', key: 'name', width: 300, sortable: false },
  { title: 'View', key: 'view', sortable: false, align: 'center' },
  { title: 'Create', key: 'add', sortable: false, align: 'center' },
  { title: 'Edit', key: 'edit', sortable: false, align: 'center' },
  { title: 'Download', key: 'download', sortable: false, align: 'center' },
  { title: 'Approve', key: 'approve', sortable: false, align: 'center' },
  // { title: 'Initiate', key: 'initiate' },
]

const isAllItemsChecked = (item: any, type: string) => {
  if (item.items.length === 0) {
    return false
  }
  return item.items.every((item: any) => item[type]['value'])
}
const isIndeterminate = (item: any, type: string) => {
  const checkedCount = item.items.filter((item: any) => item[type]['value']).length
  return checkedCount > 0 && checkedCount < item.items.length
}
const handleClickRow = (e: any, item: any, type: string) => {
  e.stopPropagation()
  const currentStatus = isAllItemsChecked(item, type)
  item.items.forEach((item: any) => {
    item[type]['value'] = !currentStatus
  })
}
const handleClickItem = (e: any, item: any, type: string) => {
  e.stopPropagation()
  item[type]['value'] = !item[type]['value']
}


defineExpose({
  getParams: () => {
    // Get all checked accessIds
    const flatList = treeToList(permissionList.value, 'items')
    const viewAccessIdList = flatList.filter(item => item.isViewed.value).map(item => item.isViewed.id)
    const addAccessIdList = flatList.filter(item => item.isAdd.value).map(item => item.isAdd.id)
    const editAccessIdList = flatList.filter(item => item.isEdit.value).map(item => item.isEdit.id)
    const downloadAccessIdList = flatList.filter(item => item.isDownload.value).map(item => item.isDownload.id)
    const approveAccessIdList = flatList.filter(item => item.isApprove.value).map(item => item.isApprove.id)
    const initiateAccessIdList = flatList.filter(item => item.isInitiate.value).map(item => item.isInitiate.id)
    const accessIdList = [...viewAccessIdList, ...addAccessIdList, ...editAccessIdList, ...approveAccessIdList, ...initiateAccessIdList, ...downloadAccessIdList]
    return {
      accessIdList,
    }
  }
})
</script>

<template>
  <form>
    <VDataTable :headers="headers" :items="permissionList" expand-on-click hide-default-footer :expanded="expandedRows">
      <!-- Expanded Row Data -->
      <template #expanded-row="slotProps">
        <tr class="v-data-table__tr" v-for="item in slotProps.item.items">

          <td>
            <ul class="my-1 pl-10">
              <li>{{ item.name }}</li>
            </ul>

          </td>

          <td>
            <div class="d-flex align-center justify-center">
              <VCheckbox hide-details :model-value="item.isViewed.value" density="compact"
                @click="(e) => handleClickItem(e, item, 'isViewed')"
                :disabled="item.isViewed && item.isViewed.disabled" />
            </div>
          </td>
          <td>
            <div class="d-flex align-center justify-center">
              <VCheckbox hide-details :model-value="item.isAdd.value" density="compact"
                @click="(e) => handleClickItem(e, item, 'isAdd')" :disabled="item.isAdd && item.isAdd.disabled" />
            </div>
          </td>
          <td>
            <div class="d-flex align-center justify-center">
              <VCheckbox hide-details :model-value="item.isEdit.value" density="compact"
                @click="(e) => handleClickItem(e, item, 'isEdit')" :disabled="item.isEdit && item.isEdit.disabled" />
            </div>
          </td>
          <td>
            <div class="d-flex align-center justify-center">
              <VCheckbox hide-details :model-value="item.isDownload.value" density="compact"
                @click="(e) => handleClickItem(e, item, 'isDownload')"
                :disabled="item.isDownload && item.isDownload.disabled" />
            </div>
          </td>
          <td>
            <div class="d-flex align-center justify-center">
              <VCheckbox hide-details :model-value="item.isApprove.value" density="compact"
                @click="(e) => handleClickItem(e, item, 'isApprove')"
                :disabled="item.isApprove && item.isApprove.disabled" />
            </div>
          </td>
          <!-- Initiate 
        <td>
          <VCheckbox hide-details
          :model-value="item.isInitiate.value"
            density="compact" @click="(e) => handleClickItem(e, item, 'isInitiate')" :disabled="item.isInitiate && item.isInitiate.disabled"/>
        </td>
        -->
        </tr>
      </template>

      <!-- Title -->
      <template #item.title="{ item }">
        <div class="d-flex align-center">
          <div class="d-flex flex-column ms-3">
            <span class="d-block font-weight-medium text-high-emphasis text-truncate">{{ item.title }}</span>
          </div>
        </div>
      </template>

      <!-- View -->
      <template #item.view="{ item }">
        <div class="d-flex align-center justify-center">
          <div>
            <VCheckbox hide-details density="compact" @click="(e) => handleClickRow(e, item, 'isViewed')"
              :model-value="isAllItemsChecked(item, 'isViewed')" :indeterminate="isIndeterminate(item, 'isViewed')"
              :disabled="item.isViewed && item.isViewed.disabled" />
          </div>
        </div>
      </template>

      <!-- Add -->
      <template #item.add="{ item }">
        <div class="d-flex align-center justify-center">
          <div>
            <VCheckbox hide-details density="compact" @click="(e) => handleClickRow(e, item, 'isAdd')"
              :model-value="isAllItemsChecked(item, 'isAdd')" :indeterminate="isIndeterminate(item, 'isAdd')"
              :disabled="item.isAdd && item.isAdd.disabled" />
          </div>
        </div>
      </template>
      <!-- Edit -->
      <template #item.edit="{ item }">
        <div class="d-flex align-center justify-center">
          <div>
            <VCheckbox hide-details density="compact" @click="(e) => handleClickRow(e, item, 'isEdit')"
              :model-value="isAllItemsChecked(item, 'isEdit')" :indeterminate="isIndeterminate(item, 'isEdit')"
              :disabled="item.isEdit && item.isEdit.disabled" />
          </div>
        </div>
      </template>
      <!-- Download -->
      <template #item.download="{ item }">
        <div class="d-flex align-center justify-center">
          <div>
            <VCheckbox hide-details density="compact" @click="(e) => handleClickRow(e, item, 'isDownload')"
              :model-value="isAllItemsChecked(item, 'isDownload')" :indeterminate="isIndeterminate(item, 'isDownload')"
              :disabled="item.isDownload && item.isDownload.disabled" />
          </div>
        </div>
      </template>

      <!-- Approve -->
      <template #item.approve="{ item }">
        <div class="d-flex align-center justify-center">
          <div>
            <VCheckbox hide-details density="compact" @click="(e) => handleClickRow(e, item, 'isApprove')"
              :model-value="isAllItemsChecked(item, 'isApprove')" :indeterminate="isIndeterminate(item, 'isApprove')"
              :disabled="item.isApprove && item.isApprove.disabled" />
          </div>
        </div>
      </template>

      <!-- Initiate 
    <template #item.initiate="{ item }">
      <div class="d-flex align-center">
        <div>
          <VCheckbox hide-details
            density="compact" @click="(e) => handleClickRow(e, item, 'isInitiate')" :model-value="isAllItemsChecked(item, 'isInitiate')" :indeterminate="isIndeterminate(item, 'isInitiate')" :disabled="item.isInitiate && item.isInitiate.disabled" />
        </div>
      </div>
    </template>
    -->
    </VDataTable>
  </form>
</template>

<style scoped lang="scss">
.v-table {
  background-color: transparent !important;
}
</style>
