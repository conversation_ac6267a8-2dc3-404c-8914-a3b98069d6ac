import { isDevelopment } from "@/utils/helpers";
import message from "@/utils/message";
import { ofetch } from "ofetch";
import { useAuthStore } from "@/stores/useAuthStore";
import {useStorage} from '@/hooks/useStorage'

const UNAUTHORIZED = 401;

let retryCount = useStorage('retryCount') || 0;

export const $api = ofetch.create({
  timeout: 10000,
  cache: 'no-cache',
  mode: 'cors',
  async onRequest({ options }) {
    // Set default request headers
    options.headers = {
      "Content-Type": "application/json",
      Accept: "*/*",
      ...options.headers,
    };

    // Read token from .env.local file in local development environment
    if (isDevelopment) {
      // skip oidc
      options.headers['oidc-skip'] = '1'
    
    }
    const accessToken = useCookie("accessToken").value;
    if (accessToken) {
      options.headers.Authorization = `Bearer ${accessToken}`;
    }

    // For POST requests, ensure data is passed correctly
    if (options.method === "POST" && options.data) {
      options.body = JSON.stringify(options.data);
    }
  },
  onRequestError: async ({error, options, request}) => {
    // check for CORS error
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      // check for CORS error
      if (retryCount < 1) {
        console.log('check for CORS error, refreshing page...');
        useStorage('retryCount', retryCount++);
        useAuthStore().clearAuth(); // 同步清理 token
        useCookie("accessToken").value = null; // 清除 cookie
        window.location.reload();
        return;
      }
    }

    // 其他错误处理...
    message.error('Request failed, please try again')
    return Promise.reject(error)
  },
  async onResponseError({ response, options }) {
    const errMsg = response._data.message || 'Something went wrong'
    const showToast = options.showToast === undefined ? true : options.showToast

    // Handle token expiration
    if (response.status === UNAUTHORIZED) {
      try {
        let query = {}
        if (isDevelopment) {
          query.name = import.meta.env.VITE_USER_NAME
          query.email = import.meta.env.VITE_USER_EMAIL
        }
        const res = await $api("/api/auth/login", { method: "GET", query });
        useAuthStore().setToken(res.data);
      } catch (error) {
        console.error("Token refresh failed:", error);
      }
    } else {
      if (showToast) {
        message.error(errMsg)
      }
    }
    return Promise.reject(response)
  },
  async onResponse({ response }) {
    retryCount = 0;
    useStorage('retryCount', 0);
    return response
  },
  retry: 0
});
