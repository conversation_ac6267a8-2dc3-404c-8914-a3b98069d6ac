// vite.config.js
import {
  VueRouterAutoImports,
  getPascalCaseRouteName
} from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/unplugin-vue-router@0.8.8_rollup@4.31.0_vue-router@4.4.0_vue@3.4.35_typescript@5.7.3___vue@3.4.35_typescript@5.7.3_/node_modules/unplugin-vue-router/dist/index.mjs";
import { defineConfig, loadEnv } from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0/node_modules/vite/dist/node/index.js";
import AutoImport from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/unplugin-auto-import@0.18.2_@vueuse+core@10.11.0_vue@3.4.35_typescript@5.7.3___rollup@4.31.0/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/unplugin-vue-components@0.27.3_@babel+parser@7.26.5_rollup@4.31.0_vue@3.4.35_typescript@5.7.3_/node_modules/unplugin-vue-components/dist/vite.js";
import Layouts from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/vite-plugin-vue-layouts@0.11.0_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0_xd2zpu7uyhdnktnjbopugqqvka/node_modules/vite-plugin-vue-layouts/dist/index.mjs";
import Unocss from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/unocss@66.0.0_postcss@8.5.1_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0__vue@3.4.35_typescript@5.7.3_/node_modules/unocss/dist/vite.mjs";
import VueDevTools from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.7_rollup@4.31.0_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29_bjx5mzrufu7ejksrwlh5fzn5im/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import VueI18nPlugin from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/@intlify+unplugin-vue-i18n@4.0.0_rollup@4.31.0_vue-i18n@9.14.3_vue@3.4.35_typescript@5.7.3__/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import VueRouter from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/unplugin-vue-router@0.8.8_rollup@4.31.0_vue-router@4.4.0_vue@3.4.35_typescript@5.7.3___vue@3.4.35_typescript@5.7.3_/node_modules/unplugin-vue-router/dist/vite.mjs";
import { fileURLToPath } from "node:url";
import svgLoader from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/vite-svg-loader@5.1.0_vue@3.4.35_typescript@5.7.3_/node_modules/vite-svg-loader/index.js";
import vue from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/@vitejs+plugin-vue@5.1.1_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0__vue@3.4.35_typescript@5.7.3_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0___lxo753tm2ivmqgt6wvozwuuyea/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vuetify from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/vite-plugin-vuetify@2.0.3_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0__vue_cazr3mt5v476wdklmry4mkknce/node_modules/vite-plugin-vuetify/dist/index.mjs";
import { webUpdateNotice } from "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/node_modules/.pnpm/@plugin-web-update-notification+vite@2.0.0_vite@5.3.5_@types+node@22.10.7_lightningcss@1.29.2_sass@1.76.0_/node_modules/@plugin-web-update-notification/vite/dist/index.js";
var __vite_injected_original_import_meta_url = "file:///C:/Users/<USER>/work/com.razer.gold.admin.portal.v2/vite.config.js";
var vite_config_default = defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd());
  console.log("Build Mode:", mode);
  console.log("Build Base URL:", env.VITE_BASE_URL);
  return {
    base: "/",
    plugins: [
      // Docs: https://github.com/posva/unplugin-vue-router
      // ℹ️ This plugin should be placed before vue plugin
      VueRouter({
        getRouteName: (routeNode) => {
          return getPascalCaseRouteName(routeNode).replace(/([a-z\d])([A-Z])/g, "$1-$2").toLowerCase();
        },
        dts: false
      }),
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => tag === "swiper-container" || tag === "swiper-slide"
          }
        }
      }),
      VueDevTools(),
      vueJsx(),
      Unocss({
        // 可以在这里添加额外配置
      }),
      // Docs: https://github.com/vuetifyjs/vuetify-loader/tree/master/packages/vite-plugin
      vuetify({
        styles: {
          configFile: "src/assets/styles/variables/_vuetify.scss"
        },
        autoImport: true,
        labComponents: true
      }),
      // Docs: https://github.com/johncampionjr/vite-plugin-vue-layouts#vite-plugin-vue-layouts
      Layouts({
        layoutsDirs: "./src/layouts/"
      }),
      // Docs: https://github.com/antfu/unplugin-vue-components#unplugin-vue-components
      Components({
        dirs: ["src/components"],
        dts: false,
        // Disable auto-generated types component.d.ts file
        resolvers: [
          (componentName) => {
            if (componentName === "VueApexCharts")
              return {
                name: "default",
                from: "vue3-apexcharts",
                as: "VueApexCharts"
              };
          }
        ]
      }),
      // Docs: https://github.com/antfu/unplugin-auto-import#unplugin-auto-import
      AutoImport({
        imports: [
          "vue",
          VueRouterAutoImports,
          "@vueuse/core",
          "@vueuse/math",
          "vue-i18n",
          "pinia"
        ],
        dirs: [
          "./src/@/utils",
          "./src/hooks/",
          "./src/utils/",
          "./src/plugins/*/composables/*"
        ],
        vueTemplate: true,
        // ℹ️ Disabled to avoid confusion & accidental usage
        ignore: ["useCookies", "useStorage"],
        eslintrc: {
          enabled: true,
          filepath: "./.eslintrc-auto-import.json"
        },
        dts: false
      }),
      // Docs: https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n#intlifyunplugin-vue-i18n
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        include: [
          fileURLToPath(
            new URL("./src/plugins/i18n/locales/**", __vite_injected_original_import_meta_url)
          )
        ]
      }),
      svgLoader(),
      webUpdateNotice({
        logVersion: true,
        notificationProps: {
          title: "New Version Available",
          description: "Please refresh the page to use the latest version.",
          buttonText: "Refresh",
          dismissButtonText: "Dismiss"
        }
      })
    ],
    define: {
      "process.env": env
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        "@themeConfig": fileURLToPath(
          new URL("./themeConfig.js", __vite_injected_original_import_meta_url)
        ),
        "@core": fileURLToPath(new URL("./src/@core", __vite_injected_original_import_meta_url)),
        "@layouts": fileURLToPath(
          new URL("./src/layouts/common", __vite_injected_original_import_meta_url)
        ),
        "@images": fileURLToPath(
          new URL("./src/assets/images/", __vite_injected_original_import_meta_url)
        ),
        "@styles": fileURLToPath(
          new URL("./src/assets/styles/", __vite_injected_original_import_meta_url)
        ),
        "@configured-variables": fileURLToPath(
          new URL(
            "./src/assets/styles/variables/_template.scss",
            __vite_injected_original_import_meta_url
          )
        ),
        "@db": fileURLToPath(
          new URL("./src/plugins/fake-api/handlers/", __vite_injected_original_import_meta_url)
        ),
        "@api-utils": fileURLToPath(
          new URL("./src/plugins/fake-api/utils/", __vite_injected_original_import_meta_url)
        )
      }
    },
    // 添加到 vite.config.js
    esbuild: {
      // 使用esbuild加速JS转换
      jsxFactory: "h",
      jsxFragment: "Fragment",
      target: "es2020"
    },
    build: {
      assetsDir: "assets",
      chunkSizeWarningLimit: 5e3,
      commonjsOptions: {
        transformMixedEsModules: true
      },
      cache: true,
      rollupOptions: {
        external: ["@plugin-web-update-notification/vue"],
        output: {
          // Add hash to ensure unique filenames
          assetFileNames: "assets/[name]-[hash][extname]",
          chunkFileNames: "assets/[name]-[hash].js"
        }
      }
    },
    optimizeDeps: {
      include: [
        "dayjs",
        "dayjs/plugin/utc",
        "dayjs/plugin/timezone",
        "vue-router",
        "pinia",
        "@vueuse/core",
        "lodash-es"
        // 添加更多常用依赖
      ],
      exclude: ["vuetify"],
      entries: ["./src/**/*.vue"]
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      proxy: {
        "/api": {
          target: env.VITE_API_URL || env.VITE_BASE_URL,
          changeOrigin: true
        }
      },
      force: false
    },
    cacheDir: ".vite"
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
