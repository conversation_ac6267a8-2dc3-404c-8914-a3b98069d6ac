<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VSwitch Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
</head>
<body>
    <div id="app">
        <v-app>
            <v-container>
                <h2>VSwitch 双向绑定测试</h2>
                
                <v-card class="pa-4 mb-4">
                    <h3>基础VSwitch测试</h3>
                    <v-switch 
                        v-model="switchValue" 
                        label="测试开关" 
                        color="primary"
                        :true-value="true"
                        :false-value="false"
                        @update:model-value="onSwitchChange"
                    ></v-switch>
                    <p>当前值: {{ switchValue }}</p>
                    <p>值类型: {{ typeof switchValue }}</p>
                </v-card>

                <v-card class="pa-4 mb-4">
                    <h3>表单数据绑定测试</h3>
                    <v-switch 
                        v-model="formData.status" 
                        label="状态开关" 
                        color="primary"
                        :true-value="true"
                        :false-value="false"
                        @update:model-value="onFormStatusChange"
                    ></v-switch>
                    <p>表单数据: {{ JSON.stringify(formData) }}</p>
                </v-card>

                <v-card class="pa-4">
                    <h3>低代码模拟测试</h3>
                    <v-switch 
                        :model-value="lowCodeData.active" 
                        label="低代码开关" 
                        color="primary"
                        :true-value="true"
                        :false-value="false"
                        @update:model-value="updateLowCodeData"
                    ></v-switch>
                    <p>低代码数据: {{ JSON.stringify(lowCodeData) }}</p>
                </v-card>
            </v-container>
        </v-app>
    </div>

    <script>
        const { createApp } = Vue;
        const { createVuetify } = Vuetify;

        const vuetify = createVuetify();

        createApp({
            data() {
                return {
                    switchValue: false,
                    formData: {
                        status: false,
                        name: 'test'
                    },
                    lowCodeData: {
                        active: false
                    }
                }
            },
            methods: {
                onSwitchChange(value) {
                    console.log('Switch changed:', value, typeof value);
                },
                onFormStatusChange(value) {
                    console.log('Form status changed:', value, typeof value);
                },
                updateLowCodeData(value) {
                    console.log('Low code data update:', value, typeof value);
                    this.lowCodeData.active = value;
                    console.log('Updated low code data:', this.lowCodeData);
                }
            }
        }).use(vuetify).mount('#app');
    </script>
</body>
</html>
