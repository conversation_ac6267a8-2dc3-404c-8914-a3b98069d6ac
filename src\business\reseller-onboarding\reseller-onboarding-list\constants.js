export const RESELLER_ONBOARD_MENU_ID = 52;
export const SUPER_ADMIN_GROUP_ID = 1;
export const RESELLER_ONBOARD_GROUP_ID = 7;
export const SUPER_ADMIN_ROLE_ID = 1;
export const RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID = 31;
export const RESELLER_ONBOARD_RESELLER_OPS_KYB_ROLE_ID = 32;
export const RESELLER_ONBOARD_COUNTRY_MANAGER_ENQUIRY_ROLE_ID = 33;
export const RESELLER_ONBOARD_COMPLIANCE_ENQUIRY_ROLE_ID = 34;
export const RESELLER_ONBOARD_COMPLIANCE_KYB_ROLE_ID = 35;
export const VIEW_ACCESS = 1;
export const ADD_ACCESS = 2;
export const APPROVE_ACCESS = 4;
export const DOWNLOAD_ACCESS = 6;

export const REJECT_REASONS = [
  "1. Sanctioned country/individual",
  "2. Refuse to provide mandatory KYB documents",
  "3. Incorporation of Prohibited Activities",
  "4. Others"
];

export const LOW_TIER_REVIEW_SECTION = [
  "1. Personal Information",
  "2. Product",
  "3. Additional Documents",
];

export const MEDIUM_TIER_REVIEW_SECTION = [
  "1. Reseller (Company)",
  "2. Business Information",
  "3. Product",
  "4. Authorized Signatory",
  "5. Additional Documents",
];

export const HIGH_TIER_REVIEW_SECTION = [
  "1. Reseller (Company)",
  "2. Business Information",
  "3. Product",
  "4. Authorized Signatory",
  "5. Company Director",
  "6. Ultimate Business Owner",
  "7. Additional Documents",
];

export const REVIEW_REASON = [
  "1. Expired document",
  "2. Blurry image/document",
  "3. Masked image/document",
  "4. Wrong selection",
  "5. Incorrect information",
  "6. Typographical error",
  "7. Others"
];

export const STATUS_MAP = {
  ENQUIRY_SUBMITTED: '1',
  ENQUIRY_REJECTED: '2',
  PENDING_ACTIVATE: '3',
  PENDING_ONBOARD: '4',
  PENDING_KYB: '5',
  KYB_SUCCESS: '6',
  PROCESSING_KYB: '7',
  KYB_FAILED: '8',
};

export const showRejectTipStatus = [STATUS_MAP.ENQUIRY_REJECTED, STATUS_MAP.KYB_FAILED];

export const PDF_PREVIEW_DIALOG_TYPE = {
  PDF_DIALOG: 0,
  FILE_DIALOG: 1,
};

export const PDF = [".pdf"];

export const IMG = [".jpg", ".png"];

export const TIFF = [".tiff"];

export const OPERATION_TYPE = {
  APPROVE: 6,
  REVIEW: 7,
  REJECT: 8,
};

export const RESELLER_TIER_TYPE = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
};

export const RESELLER_TYPE = {
  INDIVIDUAL: 1,
  COMPANY: 2,
};

export const EXPECTED_SALES_PER_MONTH = {
  BELOW_20K: 1,
  TO_20K_100K: 2,
  TO_100K_200K: 3,
  TO_200K_500K: 4,
  TO_500K_1MILLION: 5,
  ABOVE_1MILLION: 6,
};

export const enableEnquiryViewStatus = [STATUS_MAP.ENQUIRY_SUBMITTED, STATUS_MAP.ENQUIRY_REJECTED, STATUS_MAP.PENDING_ACTIVATE, STATUS_MAP.PENDING_ONBOARD];
export const enableKYBViewStatus = [STATUS_MAP.PENDING_KYB, STATUS_MAP.KYB_SUCCESS, STATUS_MAP.PROCESSING_KYB, STATUS_MAP.KYB_FAILED];
export const enableReviewAndApproveStatus = [STATUS_MAP.ENQUIRY_SUBMITTED, STATUS_MAP.PENDING_KYB];

export const TABLE_HEADER = [
  { title: "No", key: "no", fixed: true },
  { title: "Reseller ID", key: "systemAccountId", sortable: false },
  { title: "Reseller Name", key: "lastName", width: 200, sortable: false },
  { title: "Email", key: "email", sortable: false },
  { title: "Contact Number", key: "phoneCountryCode", sortable: false },
  { title: "Reseller Type", key: "resellerTypeContent", sortable: false },
  { title: "Company Name", key: "regName", width: 260, sortable: false },
  { title: "Country", key: "regCountryContent", sortable: false },
  { title: "Main Distribution Country", key: "mainDistributionCountryContent", sortable: false },
  { title: "Submitted On", key: "submittedOn", sortable: false },
  { title: "TPV Per Month", key: "expectedSalesPerMonthContent", sortable: false },
  { title: "Status", key: "statusCode", sortable: false },
  { title: "Last Modified", key: "lastModified", sortable: false },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    fixed: true,
    width: 120,
  },
]