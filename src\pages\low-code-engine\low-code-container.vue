<template>
  <div class="low-code-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <VProgressCircular indeterminate size="64" />
      <div class="loading-text">Loading Page Configuration...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-overlay">
      <h3 class="text-h6 my-2">{{ error }}</h3>
      <VBtn @click="retryLoad" color="primary" class="mt-4 w-120px">
        Retry
      </VBtn>
    </div>

    <!-- 页面内容 -->
    <div v-else-if="pageConfig" class="page-content">
      <!-- 直接渲染VNode -->
      <div class="page-components">
        <component :is="renderFunction" />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state text-[#666]">
      <span class="mdi mdi-delete-empty-outline font-size-[64px]"></span>
      <h3 class="text-h6 my-2">Page Not Found</h3>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, watchEffect, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { render } from '@/business/low-code-engine/renderer/index.js'
import { useLowCodeAPI } from '@/business/low-code-engine/api/useLowCodeAPI.js'
import { generatePagesFromBackendMeta } from '@/business/low-code-engine/editor/pageGenerator.js'

// 响应式数据
const isLoading = ref(true)
const error = ref(null)
const pageConfig = ref(null)
const renderFunction = ref(null)
const { getPageJsonByPath, getMetadata } = useLowCodeAPI()

// 路由
const route = useRoute()
const router = useRouter()

// 简化的路径解析函数
function parsePageUrl(url) {
  const cleanPath = url.replace(/^\/low-code\//, '')
  if (!cleanPath) return null

  const segments = cleanPath.split('/').filter(segment => segment.length > 0)
  if (segments.length === 0) return null

  return {
    scenario: segments[0],
    pageType: segments[1] || 'list'
  }
}

// 计算属性：从路由路径解析页面信息
const pageInfo = computed(() => {
  return parsePageUrl(route.path)
})


// 生成页面配置的简化函数
async function generatePageConfig(scenario, pageType) {
  try {
    console.log('生成页面配置:', { scenario, pageType })

    // 获取场景元数据
    const metaResponse = await getMetadata({ scenario })
    if (!metaResponse || !metaResponse.data) {
      throw new Error('场景元数据不存在')
    }

    const backendMeta = metaResponse.data
    console.log('获取到场景元数据:', backendMeta)

    // 使用页面生成器生成配置
    const pages = generatePagesFromBackendMeta(backendMeta)
    const config = pages[pageType]

    if (!config) {
      throw new Error(`页面类型 ${pageType} 不存在`)
    }

    console.log('页面配置生成成功:', config)
    return config

  } catch (error) {
    console.error('生成页面配置失败:', error)
    throw error
  }
}

// 加载页面配置
async function loadPageConfig() {
  if (!pageInfo.value) {
    error.value = '无效的页面路径'
    isLoading.value = false
    return
  }

  try {
    isLoading.value = true
    error.value = null

    // 首先尝试从后端获取页面配置
    const config = await getPageJsonByPath({ path: route.path })

    if (config && config.data) {
      try {
        pageConfig.value = JSON.parse(config.data.layout)
      } catch (err) {
        console.error('页面配置格式无效:', err)
        throw new Error('页面配置格式无效')
      }
      console.log('页面配置加载成功:', pageConfig.value)
      renderPageComponents()
      return
    }


  } catch (err) {
    console.error('加载页面配置失败:', err)
    error.value = err.message || 'loading page config failed'
  } finally {
    isLoading.value = false
  }
}

// 渲染页面组件
function renderPageComponents() {
  if (!pageConfig.value || !pageConfig.value.components) {
    console.warn('页面配置或组件不存在')
    return
  }

  try {
    // 创建一个渲染函数组件
    renderFunction.value = {
      name: 'LowCodePage',
      setup() {
        return () => {
          try {
            // 使用渲染器渲染组件
            const vnodes = render(pageConfig.value)
            console.log('渲染器返回的VNodes:', vnodes)

            if (vnodes && Array.isArray(vnodes)) {
              // 如果有多个根节点，用div包装
              return vnodes.length === 1 ? vnodes[0] : h('div', vnodes)
            } else {
              console.warn('渲染器返回空结果')
              return h('div', '渲染失败：无内容')
            }
          } catch (err) {
            console.error('渲染组件失败:', err)
            return h('div', `渲染失败: ${err.message}`)
          }
        }
      }
    }

    console.log('页面渲染函数创建成功')
  } catch (err) {
    console.error('创建渲染函数失败:', err)
    error.value = '创建渲染函数失败: ' + err.message
  }
}

// 重试加载
function retryLoad() {
  // 直接重新加载，不需要缓存管理
  loadPageConfig()
}

// 监听路由变化
watchEffect(() => {
  if (route.path.startsWith('/low-code')) {
    loadPageConfig()
  }
})


</script>

<style lang="scss" scoped>
.low-code-container {
  min-height: 100vh;

  .loading-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;

    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #666;
    }
  }

  .error-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 400px;
    i {
      display: block;
    }
  }
}
</style>