# 属性面板同步更新测试说明

## 问题描述
之前在低代码平台中，当用户在编辑区删除组件的某些元素（如按钮、筛选字段、表格列等）时，虽然编辑区的内容被删除了，但属性面板中对应的配置项并没有被同步删除，导致属性面板显示的内容与实际组件状态不一致。

## 解决方案
添加了 `updatePropertyConfigListField` 函数来同步更新组件的 `propertyConfig` 配置，确保属性面板与组件实际状态保持一致。

## 修改内容

### 1. 新增核心同步函数
```javascript
function updatePropertyConfigListField(fieldKey, index, operation, value = null)
```

该函数负责：
- 查找组件属性配置中对应的列表字段
- 根据操作类型（add/remove/update）同步更新配置
- 处理模板表达式和直接数组两种数据格式
- 发送组件更新事件

### 2. 修改删除函数
在以下函数中添加了属性配置同步更新：
- `removeListItem` - 删除列表项时同步删除属性配置
- 支持按钮、筛选字段、表格列等不同类型的删除

### 3. 修改添加函数
在以下函数中添加了属性配置同步更新：
- `addListItem` - 添加列表项时同步添加到属性配置

### 4. 修改更新函数
在以下函数中添加了属性配置同步更新：
- `updateListItem` - 更新列表项时同步更新属性配置

## 测试步骤

### 测试删除功能
1. 选择一个包含按钮列表的组件（如 TitleBar）
2. 在属性面板中查看按钮列表
3. 点击删除按钮
4. 验证：
   - 编辑区中的按钮被删除
   - 属性面板中的按钮配置项也被删除
   - 两者保持同步

### 测试添加功能
1. 选择一个支持添加项目的组件
2. 在属性面板中点击添加按钮
3. 验证：
   - 编辑区中添加了新的元素
   - 属性面板中也显示了新的配置项
   - 两者保持同步

### 测试更新功能
1. 选择一个列表项
2. 修改其属性值
3. 验证：
   - 编辑区中的元素属性被更新
   - 属性面板中的配置也被更新
   - 两者保持同步

## 支持的字段类型
- `buttons` - 按钮列表
- `filters` - 筛选字段列表
- `columns` - 表格列列表
- `fields` - 表单字段列表
- 其他自定义列表类型

## 注意事项
1. 函数会自动检测数据格式（模板表达式 vs 直接数组）
2. 对于模板表达式，会保持表达式不变，但确保数据源已更新
3. 所有操作都会触发组件更新事件，确保变更被正确保存
4. 包含完整的错误处理和日志记录

## 预期效果
修复后，用户在编辑区进行的任何增删改操作都会同步反映到属性面板中，确保界面状态的一致性，提升用户体验。
