<template>
  <LowCodeLayout navigation-value="index-manager">
    <div class="index-manager">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">Index Manager</h1>
        <p class="page-subtitle">Define business scenarios and index structures</p>
      </div>

      <!-- Business Scenarios 区域 -->
      <div class="scenarios-section">
        <div class="section-header">
          <div class="section-title">
            <VIcon class="mr-2" color="success">mdi-database</VIcon>
            Database Indexes
          </div>
        </div>
        <VRow>
          <VCol cols="12" md="4">
            <AppAutocomplete v-model="selectedScenario" :items="scenarios" item-title="scenario" item-value="scenario"
              return-object label="Select Scenario" filterable variant="outlined" density="compact" class="mb-4"
              hide-details />
            <div v-if="selectedScenario" :key="selectedScenario.id" class="scenario-card selected">
              <h3 class="scenario-name">{{ selectedScenario.scenario }}</h3>
              <p class="scenario-description">{{ selectedScenario.description }}</p>
              <div class="scenario-stats">
                <span class="stat-item">
                  <VIcon size="12">mdi-format-list-bulleted</VIcon>
                  {{ selectedScenario.fields ? selectedScenario.fields.length : 0 }} fields
                </span>
                <span class="stat-item">
                  <VIcon size="12">mdi-key</VIcon>
                  {{ selectedScenario.indexes ? selectedScenario.indexes.length : 0 }} indexes
                </span>
                <span class="stat-item">
                  <VIcon size="12">mdi-clock</VIcon>
                  {{ selectedScenario.lastModified }}
                </span>
              </div>
            </div>
          </VCol>
        </VRow>
      </div>

      <!-- Field Configuration 区域 -->
      <div v-if="selectedScenario" class="index-config-section">
        <div class="section-header">
          <div class="section-title">
            Index Configuration - {{ selectedScenario.scenario }}
          </div>
          <div class="action-buttons">
            <VBtn variant="flat" color="primary" @click="handleAddIndex">
              <VIcon class="mr-1">mdi-plus</VIcon>
              Add Index
            </VBtn>
          </div>
        </div>

        <!-- 字段配置表格 -->
        <div class="table-container">
          <VDataTableServer :headers="indexHeaders" :items="selectedScenario.indexList" class="index-table"
            item-key="indexName" hide-default-footer>
            <template #item.indexName="{ item }">
              <code class="index-name-code">{{ item.indexName }}</code>
            </template>


            <template #item.active="{ item }">
              <VChip v-if="item.active" size="small" color="success" variant="flat">
                Active
              </VChip>
              <VChip v-else size="small" color="error" variant="flat">
                Inactive
              </VChip>
            </template>

            <template #item.subscribeSystem="{ item }">
              <template v-if="item.subscribeSystem">
                <VChip v-for="system in item.subscribeSystem" :key="system" size="small" color="warning" variant="flat"
                  class="mr-2">
                  {{ system }}
                </VChip>
              </template>
              <template v-else>
                <VChip size="small" color="error" variant="flat">
                  None
                </VChip>
              </template>

            </template>

            <template #item.unique="{ item }">
              <VChip v-if="item.unique" size="small" color="info" variant="flat">
                {{ item.unique }}
              </VChip>

            </template>





            <template #item.actions="{ item }">
              <div class="action-icons">
                <VBtn icon size="small" variant="text" color="success">
                  <VIcon size="16">mdi-pencil</VIcon>
                </VBtn>
                <VBtn icon size="small" variant="text" color="error">
                  <VIcon size="16">mdi-delete</VIcon>
                </VBtn>
              </div>
            </template>

            <!-- pagination -->
            <template #bottom>
              <TablePagination :page="page" :items-per-page="itemsPerPage" :total-items="total"
                @update:page="handlePageChange">
                <div class="d-flex gap-3">
                  <AppSelect :model-value="itemsPerPage" :items="paginationOptions"
                    @update:model-value="handleItemsPerPageChange" />
                </div>
              </TablePagination>
            </template>
          </VDataTableServer>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <VIcon size="64" color="#666" class="mb-4">mdi-database-outline</VIcon>
        <h3 class="empty-title">Select a Scenario</h3>
        <p class="empty-subtitle">
          Choose a business scenario above to view and manage its index configuration
        </p>
      </div>
    </div>
    <IndexDialog v-model="indexDialogVisible" :formData="indexDialogData" @submit="handleSubmit" />
  </LowCodeLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLowCodeAPI } from '@/business/low-code-engine/api/useLowCodeAPI'
import LowCodeLayout from '@/business/low-code-engine/layout/LowCodeLayout.vue'
import { useScenarioConfig } from '@/business/low-code-engine/hooks/useScenarioConfig'

import { dateUtil } from '@/utils/day'
import { generatePagesFromBackendMeta } from '@/business/low-code-engine/editor/pageGenerator'
import { usePageGeneration } from '@/business/low-code-engine/hooks/usePageGeneration'
import IndexDialog from '@/business/low-code-engine/index-manager/IndexDialog.vue'

definePage({
  alias: '/low-code-engine/index-manager',
  name: 'low-code-engine-index-manager',
  meta: {
    layout: 'blank',
    public: true,
  },
})

// ===== 状态 =====
const router = useRouter()
const {
  addMetaField,
  getMetadata,
  createMetaIndex,
  getIndexes
} = useLowCodeAPI()

const { savePageContent, getPageContent } = usePageGeneration()

const { scenarioConfigs, selectedScenario, setCurrentScenario } = useScenarioConfig()

const page = ref(1)
const itemsPerPage = ref(10)
const total = ref(0)
const paginationOptions = ref([10, 20, 50, 100])


const indexDialogVisible = ref(false)
const indexDialogData = ref(null)

// 场景数据
const scenarios = ref([])

watchEffect(() => {
  scenarios.value = Array.from(scenarioConfigs.value.values())
  if (!selectedScenario.value) {
    if (scenarios.value.length > 0) {
      setCurrentScenario(scenarios.value[0])
    }
  }
})

watch(selectedScenario, (newVal) => {
  if (newVal) {
    init()
  }
}, { immediate: true })





// 表格配置
const indexHeaders = [
  { title: 'Index Name', key: 'indexName', sortable: true },
  { title: 'Type', key: 'type', sortable: true },
  { title: 'Fields', key: 'indexFields', sortable: true },
  { title: 'Status', key: 'active', sortable: false },
  { title: 'SubscribeSystem', key: 'subscribeSystem', sortable: false },
  { title: 'Unique', key: 'unique', sortable: false },
]


// ===== 方法 =====
function init() {
  getMetaFields()
  getMetaIndexes()
}

async function getMetaFields() {
  const response = await getMetadata({ scenario: selectedScenario.value.scenario })
  selectedScenario.value.metaFields = response.data.fields.map(field => {
    return {
      value: field.name,
      title: field.name
    }
  })
  selectedScenario.value.fields = response.data.fields || []
  selectedScenario.value.indexes = response.data.indexes || []
  const lastModified = response.data.updatedDateTime ? response.data.updatedDateTime : response.data.createdDateTime
  selectedScenario.value.lastModified = dateUtil.format(lastModified, 'YYYY-MM-DD HH:mm:ss')
}

async function getMetaIndexes() {
  const response = await getIndexes({ scenario: selectedScenario.value.scenario, current: page.value, size: itemsPerPage.value })
  selectedScenario.value.indexList = response.data.records
  total.value = response.data.total
}

function handleAddIndex() {
  indexDialogVisible.value = true
  indexDialogData.value = {
    scenarioId: selectedScenario.value.id,
    metaFields: selectedScenario.value.metaFields,
    mode: 'add',
    indexName: '',
    indexFields: "",
    type: 'Server',
    unique: true
  }
}

async function handleSubmit(data) {
  await createMetaIndex({
    "scenarioId": data.scenarioId,
    "indexName": data.indexName,
    "indexFields": data.indexFields,
    "type": data.type, //SERVER or CLIENT
    "unique": data.unique
  })
  indexDialogVisible.value = false
  init()
}

function handlePageChange(page) {
  page.value = page
  getMetaIndexes()
}

function handleItemsPerPageChange(itemsPerPage) {
  itemsPerPage.value = itemsPerPage
  getMetaIndexes()
}

</script>

<style scoped>
.index-manager {
  padding: 24px;
  background: #0f0f0f;
  min-height: calc(100vh - 68px);
  color: white;
}

/* 页面标题 */
.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #aaa;
  font-size: 1rem;
  margin: 0;
}

/* 区域样式 */
.scenarios-section {
  margin-bottom: 32px;
}

.index-config-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #333;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 场景选择 */
.scenario-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.scenario-card.selected {
  border-color: #4caf50;
  background: #202d23;
}

.scenario-name {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.scenario-description {
  color: #ccc;
  font-size: 0.875rem;
  margin: 0 0 16px 0;
  line-height: 1.5;
  min-height: 40px;
}

.scenario-stats {
  display: flex;
  gap: 24px;
  font-size: 0.8rem;
  color: #888;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 表格样式 */
.table-container {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.index-table) {
  background: #1a1a1a !important;
  color: white !important;
}

:deep(.index-table .v-data-table__thead) {
  background: #111 !important;
}

:deep(.index-table .v-data-table-header__content) {
  color: white !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

:deep(.index-table .v-data-table__td) {
  border-bottom: 1px solid #333 !important;
  color: white !important;
  padding: 12px 16px !important;
}

:deep(.index-table .v-data-table__tr:hover) {
  background: rgba(76, 175, 80, 0.05) !important;
}

.index-name-code {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.875rem;
}

/* 字段类型芯片样式 */
.type-chip {
  background-color: #333 !important;
  color: #ccc !important;
  border: 1px solid #555 !important;
}

.text-disabled {
  color: #666;
}

.default-value {
  color: #aaa;
  font-style: italic;
}

.action-icons {
  display: flex;
  gap: 4px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 64px 24px;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  margin-top: 24px;
}

.empty-title {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.empty-subtitle {
  color: #666;
  font-size: 0.875rem;
  margin: 0;
}

/* 对话框样式 */
:deep(.dialog-card) {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
}

:deep(.dialog-header) {
  background: #1a1a1a !important;
  color: white !important;
  border-bottom: 1px solid #333 !important;
}

:deep(.dialog-content) {
  background: #1a1a1a !important;
  color: white !important;
  padding: 24px !important;
  /* Increased padding */
  min-height: 300px;
  /* Added min-height */
}

:deep(.dialog-actions) {
  background: #1a1a1a !important;
  border-top: 1px solid #333 !important;
}

/* 表单控件样式 */
:deep(.v-text-field .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 6px !important;
}

:deep(.v-text-field .v-field__input) {
  color: white !important;
}

:deep(.v-text-field .v-label) {
  color: #888 !important;
}

:deep(.v-select .v-field) {
  background: #2a2a2a !important;
  border: 1px solid #444 !important;
  border-radius: 6px !important;
}

:deep(.v-select .v-field__input) {
  color: white !important;
}

.switches-row {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

:deep(.v-switch .v-label) {
  color: white !important;
}

/* 按钮样式 */
:deep(.v-btn) {
  text-transform: none;
  font-weight: 500;
}

:deep(.v-btn--variant-outlined) {
  border-color: #4caf50 !important;
  color: #4caf50 !important;
}

:deep(.v-btn--variant-outlined:hover) {
  background: rgba(76, 175, 80, 0.1) !important;
}

/* 芯片样式 */
:deep(.v-chip) {
  border-radius: 4px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
}
</style>
