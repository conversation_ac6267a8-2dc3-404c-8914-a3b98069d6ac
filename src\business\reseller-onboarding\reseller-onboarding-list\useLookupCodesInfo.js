export const useLookupCodesInfo = (lookupType) => {
  const RESELLER_STATUS_LOOKUP_TYPE = "21";
  const COUNTRY_PHONE_LOOKUP_TYPE = "12";
  const ENQUIRY_PASSED = "3";
  const lookupCodeList = ref([]);

  const loadLookupCodeList = async () => {
    try {
      const res = await $api(
        `/api/common/v1/lookup/lookupCodes?lookupType=${lookupType}&isActive=true`,
        {
          method: "GET",
        }
      );

      if (lookupType === COUNTRY_PHONE_LOOKUP_TYPE) {
        lookupCodeList.value = res?.data?.map((item) => {
          return {
            ...item,
            title: `+${item.name}`,
            value: item.code,
          };
        });
      } else {
        lookupCodeList.value = res?.data?.map((item) => {
          return {
            ...item,
            title: item.name,
            value: item.code,
          };
        });
      }

      if (lookupType === RESELLER_STATUS_LOOKUP_TYPE) {
        lookupCodeList.value = lookupCodeList.value.filter(
          (item) => item.code !== ENQUIRY_PASSED
        );
      }
    } catch (error) {
      console.error("Faile to load lookupcodes data:", error);
    }
  };

  onMounted(() => {
    loadLookupCodeList();
  });

  return { lookupCodeList, loadLookupCodeList };
};
