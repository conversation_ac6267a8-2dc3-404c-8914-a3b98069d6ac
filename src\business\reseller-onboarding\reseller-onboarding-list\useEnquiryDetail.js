import message from "@/utils/message";
import { onMounted } from "vue";

export const useEnquiryDetail = (resellerOnboardId) => {
  const enquiryInfo = ref({});
  const errorFlag = ref(false);

  const loadEnquiryInfo = async () => {
    try {
      if (resellerOnboardId) {
        const res = await $api(
          `/api/reseller-onboard/v1?onboardId=${resellerOnboardId}`,
          {
            method: "GET",
          },
        );

        enquiryInfo.value = res?.data;
      } else {
        errorFlag.value = true;
        message.error("Reseller Onboard ID is not provided");
      }
    } catch (error) {
      errorFlag.value = true;
      console.error("Faile to load reseller enquiry data:", error);
    }
  };

  onMounted(() => {
    loadEnquiryInfo();
  });

  return { enquiryInfo, errorFlag, loadEnquiryInfo };
};
