// const emailRouteComponent = () => import('@/pages/apps/email/index.vue')
// 👉 Redirects
export const redirects = [
  // ℹ️ We are redirecting to different pages based on role.
  // NOTE: Role is just for UI purposes. ACL is based on abilities.
  {
    path: "/",
    name: "index",
  },
  {
    path: "/pages/user-profile",
    name: "pages-user-profile",
    redirect: () => ({
      name: "pages-user-profile-tab",
      params: { tab: "profile" },
    }),
  },
  {
    path: "/pages/account-settings",
    name: "pages-account-settings",
    redirect: () => ({
      name: "pages-account-settings-tab",
      params: { tab: "account" },
    }),
  },
];

export const routes = [
  // 低代码测试页面
  {
    path: '/low-code-test',
    name: 'low-code-test',
    component: () => import('@/pages/low-code-engine/low-code-test.vue'),
    meta: {
      layout: 'default',
      title: '低代码路由测试',
    },
  },
  
  // 低代码统一容器路由 - 拦截所有 /low-code/* 路径
  {
    path: '/low-code/:path?',
    name: 'low-code-container',
    component: () => import('@/pages/low-code-engine/low-code-container.vue'),
    meta: {
      layout: 'default',
      title: '低代码页面',
    },
    props: true,
  },
  
  // 低代码编辑器路由
  {
    path: '/low-code-engine/editor',
    name: 'low-code-editor',
    component: () => import('@/pages/low-code-engine/editor.vue'),
    meta: {
      layout: 'default',
      title: '低代码编辑器',
    },
  },
  
  // 低代码引擎主页
  {
    path: '/low-code-engine',
    name: 'low-code-engine',
    component: () => import('@/pages/low-code-engine/index.vue'),
    meta: {
      layout: 'default',
      title: '低代码引擎',
    },
  },
];
