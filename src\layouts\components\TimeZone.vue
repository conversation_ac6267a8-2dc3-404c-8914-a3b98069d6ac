<template>
  <div class="d-flex align-center me-2">
    <AppSelect v-model="timezoneValue" :items="timezoneItems" @update:model-value="handleTimezoneChange" size="small"></AppSelect>
  </div>
</template>

<script setup>
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// register plugins
dayjs.extend(utc)
dayjs.extend(timezone)

const timezoneValue = ref(useStorage('timezone', 'Asia/Shanghai'))
const timezoneItems = [
  { title: 'UTC+0 Timezone', value: 'UTC' },
  { title: 'UTC+8 Timezone', value: 'Asia/Shanghai' },
]
const handleTimezoneChange = (value) => {
  timezoneValue.value = value
  useStorage('timezone', value)
  window.location.reload()
}
</script>

<style scoped></style>