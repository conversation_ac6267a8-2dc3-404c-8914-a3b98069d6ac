<script setup>
import { h, resolveComponent, watch, getCurrentInstance } from 'vue'
import * as VuetifyComponents from 'vuetify/components'
import { resolveExpression, getComponentByName, isHtmlTag } from '@/business/low-code-engine/renderer/utils'

// 导入所有可能用到的自定义组件
import FilterPanel from '@/components/FilterPanel.vue'
import AppTextField from '@/components/AppTextField.vue'
import AppSelect from '@/components/AppSelect.vue'
import AppDateTimePicker from '@/components/AppDateTimePicker.vue'
import AppAutocomplete from '@/components/AppAutocomplete.vue'
import AppCombobox from '@/components/AppCombobox.vue'
import AppTextarea from '@/components/AppTextarea.vue'
import TablePagination from '@/components/TablePagination.vue'
import TablePaginationLowCode from '@/components/TablePaginationLowCode.vue'

const props = defineProps({
  nodes: {
    type: [Array, Object],
    default: () => []
  },
  context: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['component-click'])

// 🔥 监控context变化
watch(() => props.context, (newContext) => {
  console.log('🚨 RenderNode: context发生变化!')
  console.log('🚨 RenderNode: 新context.headers长度:', newContext?.headers?.length || 0)
  if (newContext?.headers?.length > 0) {
    console.log('✅ RenderNode: headers接收成功!', newContext.headers.slice(0, 3))
  } else {
    console.log('❌ RenderNode: headers为空，context包含键:', Object.keys(newContext || {}))
  }
}, { immediate: true, deep: true })

// 移除本地的isHtmlTag函数，使用从utils导入的版本

// 🔥 修复：自定义组件映射，注册所有低代码可能用到的组件
const customComponents = {
  FilterPanel,
  AppTextField,
  AppSelect,
  AppDateTimePicker,
  AppAutocomplete,
  AppCombobox,
  AppTextarea,
  TablePagination,
  TablePaginationLowCode
}

function getComponent(type) {
  console.log(`🔍 RenderNode: 尝试解析组件 ${type}`)

  // 获取当前组件实例以获取应用上下文
  const instance = getCurrentInstance()
  const context = {
    appContext: instance?.appContext
  }

  console.log(`🔍 RenderNode: 应用上下文存在:`, !!context.appContext)

  // 使用统一的组件解析逻辑
  const component = getComponentByName(type, context)
  console.log(`🔍 RenderNode: getComponentByName 返回:`, typeof component, component)

  // 如果getComponentByName返回的是字符串，说明是HTML标签或未找到的组件
  if (typeof component === 'string') {
    console.log(`🔍 RenderNode: 尝试使用 resolveComponent 解析 ${type}`)
    // 最后尝试使用resolveComponent
    try {
      const resolved = resolveComponent(type)
      console.log(`✅ RenderNode: resolveComponent 成功解析 ${type}:`, resolved)
      return resolved
    } catch (e) {
      console.warn(`❌ RenderNode: Component ${type} not found, using as HTML tag`, e)
      return type
    }
  }

  console.log(`✅ RenderNode: 成功解析组件 ${type}`)
  return component
}

function renderNode(node) {
  if (!node) return null
  
  if (Array.isArray(node)) {
    return node.map((item, index) => renderNode(item))
  }
  
  // 🔥 修复：更严格的数据验证
  if (typeof node !== 'object' || node === null) {
    console.warn('Invalid node type:', typeof node, node)
    return null
  }
  
  const { type, props: nodeProps = {}, children, events, slots, model, ...otherAttrs } = node
  
  if (!type || typeof type !== 'string') {
    console.warn('Node missing or invalid type:', { type, node })
    return null
  }
  
  // 🔥 修复：过滤掉无效的组件类型
  if (type.includes('comp_') || type === '卡片' || typeof type === 'object') {
    console.warn('Invalid component type detected:', type)
    return null
  }
  
  try {
    const Component = getComponent(type)
    console.log(`RenderNode: 渲染组件 ${type}`, { node, Component })
    
    // 🔥 针对VDataTableServer的特殊调试
    if (type === 'VDataTableServer') {
      console.log('RenderNode: VDataTableServer - 原始node:', node)
      console.log('RenderNode: VDataTableServer - nodeProps:', nodeProps)
      console.log('RenderNode: VDataTableServer - context:', props.context)
    }
    
    const vNodeProps = {}

    // 🔥 修复：应用别名默认属性
    if (Component._aliasDefaults) {
      console.log(`🔧 RenderNode: 应用 ${type} 的默认属性:`, Component._aliasDefaults)
      Object.assign(vNodeProps, Component._aliasDefaults)
    }

    // 🔥 修复：处理props，解析表达式
    let textContent = null
    
    Object.keys(nodeProps).forEach(key => {
      let value = nodeProps[key]
      
      // 🔥 关键修复：特殊处理表达式解析
      if (typeof value === 'string' && value.includes('{{')) {
        try {
          value = resolveExpression(value, props.context)
          
          // 🔥 特殊调试：如果是headers相关的表达式
          if (key === 'headers') {
            console.log(`RenderNode: headers表达式解析结果:`, value)
            console.log(`RenderNode: headers结果类型:`, typeof value)
            console.log(`RenderNode: headers是否为数组:`, Array.isArray(value))
          }
        } catch (error) {
          console.warn(`表达式解析失败: ${value}`, error)
          
          // 🔥 修复：针对特定表达式提供正确的兜底值
          if (key === 'headers' && value.includes('headers')) {
            // 直接从context中获取headers
            value = props.context?.headers || []
            console.log(`RenderNode: headers表达式解析失败，使用兜底值:`, value)
          }
          else if (value.includes('isLoading')) value = false
          else if (value.includes('selectedIds')) value = []
          else if (value.includes('listData') || value.includes('accounts')) value = []
          else if (value.includes('totalCount') || value.includes('totalUsers')) value = 0
          else if (value.includes('page')) value = 1
          else value = null
        }
      }
      
      // 特殊处理text属性，转换为文本内容
      if (key === 'text') {
        textContent = value
        return // 不添加到vNodeProps中
      }
      
      vNodeProps[key] = value
    })
    
    // 🔥 针对VDataTableServer的props调试
    if (type === 'VDataTableServer') {
      console.log('RenderNode: VDataTableServer - 处理后的vNodeProps:', vNodeProps)
      console.log('RenderNode: VDataTableServer - headers值:', vNodeProps.headers)
    }
    
    // 🔥 修复：处理特殊属性
    // 处理 v-model
    if (model && typeof model === 'string' && model.includes('{{')) {
      try {
        const modelValue = resolveExpression(model, props.context)
        vNodeProps['modelValue'] = modelValue
      } catch (error) {
        console.warn(`Model表达式解析失败: ${model}`, error)
        vNodeProps['modelValue'] = []
      }
      // 添加update事件处理
      vNodeProps['onUpdate:modelValue'] = (value) => {
        console.log('RenderNode: Model value updated:', value)

        // 解析model表达式，获取字段名
        const modelMatch = model.match(/{{formData\.(.+?)}}/)
        if (modelMatch) {
          const fieldName = modelMatch[1]
          console.log('RenderNode: Updating field:', fieldName, 'with value:', value)

          // 直接更新context中的formData
          if (props.context?.formData) {
            props.context.formData[fieldName] = value
            console.log('RenderNode: formData updated via v-model:', props.context.formData)
          }
        }
      }
    }
    
    // 处理 v-if
    if (otherAttrs['v-if']) {
      const condition = resolveExpression(otherAttrs['v-if'], props.context)
      if (!condition) {
        return null // 不渲染此组件
      }
    }
    
    // 处理事件
    if (events) {
      Object.keys(events).forEach(eventName => {
        const eventKey = `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1).replace(/[:-]/g, '')}`
        const actions = events[eventName]

        vNodeProps[eventKey] = (...args) => {
          console.log(`Event ${eventName} triggered:`, args)

          // 处理每个action
          if (Array.isArray(actions)) {
            actions.forEach(actionMeta => {
              if (actionMeta.action === 'updateFormData') {
                // 特殊处理updateFormData
                const fieldName = actionMeta.params?.[0]
                const eventValue = args[0] // VSwitch的值

                console.log('RenderNode: updateFormData called:', fieldName, eventValue)

                // 直接更新context中的formData
                if (fieldName && props.context?.formData) {
                  props.context.formData[fieldName] = eventValue
                  console.log('RenderNode: formData updated:', props.context.formData)
                }
              } else {
                console.log(`RenderNode: 未处理的action: ${actionMeta.action}`)
              }
            })
          }
        }
      })
    }
    
    // 添加组件点击事件处理 - 只有有 propertyConfig 的组件才能被选中
    if (node.id && node.propertyConfig) {
      vNodeProps.onClick = (event) => {
        event.stopPropagation()
        console.log('🔥 RenderNode: 可编辑组件点击:', node.id, node.type)
        emit('component-click', node.id)
      }
      
      // 添加可编辑组件的样式
      vNodeProps.style = {
        ...vNodeProps.style,
        cursor: 'pointer',
        position: 'relative'
      }
      
      // 添加选中效果的样式类
      if (typeof vNodeProps.class === 'string') {
        vNodeProps.class = [vNodeProps.class, 'editable-component']
      } else if (typeof vNodeProps.class === 'object') {
        vNodeProps.class = {
          ...vNodeProps.class,
          'editable-component': true
        }
      } else {
        vNodeProps.class = 'editable-component'
      }
    }
    
    // 处理子组件，传递context
    let childNodes = null
    if (children && children.length > 0) {
      childNodes = children.map(child => renderNode(child))
    }
    
    // 🔥 修复：处理text属性作为文本内容
    if (textContent && !childNodes) {
      childNodes = [textContent]
    } else if (textContent && childNodes) {
      childNodes = [textContent, ...childNodes]
    }
    
    // 处理插槽
    if (slots) {
      const slotNodes = {}
      Object.keys(slots).forEach(slotName => {
        slotNodes[slotName] = () => renderNode(slots[slotName])
      })
      return h(Component, vNodeProps, slotNodes)
    }
    
    // 渲染组件
    if (childNodes) {
      return h(Component, vNodeProps, { default: () => childNodes })
    } else {
      return h(Component, vNodeProps)
    }
    
  } catch (error) {
    console.error('RenderNode 渲染错误:', error, node)
    return h('div', { 
      style: 'color: red; padding: 8px; border: 1px solid red; margin: 4px;' 
    }, `渲染错误: ${error.message}`)
  }
}
</script>

<template>
  <template v-for="(node, idx) in nodes" :key="idx">
    <component :is="renderNode(node)" />
  </template>
</template>