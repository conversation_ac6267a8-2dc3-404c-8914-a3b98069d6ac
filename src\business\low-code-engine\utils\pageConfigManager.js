import { generatePagesFromBackendMeta } from '../editor/pageGenerator.js'
import { useLowCodeAPI } from '../api/useLowCodeAPI.js'

/**
 * 页面配置管理器
 * 负责页面配置的获取、缓存、生成和管理
 */
export class PageConfigManager {
  constructor() {
    this.cache = new Map() // 配置缓存
    this.api = useLowCodeAPI()
  }

  /**
   * 获取页面配置
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   * @param {boolean} forceRefresh - 是否强制刷新缓存
   * @returns {Promise<Object>} 页面配置
   */
  async getPageConfig(scenario, pageType = 'list', forceRefresh = false) {
    const cacheKey = `${scenario}:${pageType}`
    
    // 检查缓存
    if (!forceRefresh && this.cache.has(cacheKey)) {
      console.log('从缓存获取页面配置:', cacheKey)
      return this.cache.get(cacheKey)
    }

    try {
      console.log('获取页面配置:', { scenario, pageType })
      
      // 尝试从API获取配置
      const config = await this.api.getPageConfig(scenario, pageType)
      
      if (config) {
        // 缓存配置
        this.cache.set(cacheKey, config)
        console.log('页面配置获取成功:', config)
        return config
      }
      
      throw new Error('页面配置不存在')
      
    } catch (error) {
      console.error('获取页面配置失败:', error)
      throw error
    }
  }

  /**
   * 生成页面配置
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   * @returns {Promise<Object>} 生成的页面配置
   */
  async generatePageConfig(scenario, pageType = 'list') {
    try {
      console.log('生成页面配置:', { scenario, pageType })
      
      // 获取场景元数据
      const metaResponse = await this.api.getMetadata({ scenario })
      if (!metaResponse || !metaResponse.data) {
        throw new Error('场景元数据不存在')
      }
      
      const backendMeta = metaResponse.data
      console.log('获取到场景元数据:', backendMeta)
      
      // 使用页面生成器生成配置
      const pages = generatePagesFromBackendMeta(backendMeta)
      const config = pages[pageType]
      
      if (!config) {
        throw new Error(`页面类型 ${pageType} 不存在`)
      }
      
      console.log('页面配置生成成功:', config)
      return config
      
    } catch (error) {
      console.error('生成页面配置失败:', error)
      throw error
    }
  }

  /**
   * 保存页面配置
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   * @param {Object} config - 页面配置
   * @returns {Promise<Object>} 保存结果
   */
  async savePageConfig(scenario, pageType, config) {
    try {
      console.log('保存页面配置:', { scenario, pageType })
      
      const result = await this.api.savePageConfig(scenario, pageType, config)
      
      // 更新缓存
      const cacheKey = `${scenario}:${pageType}`
      this.cache.set(cacheKey, config)
      
      console.log('页面配置保存成功:', result)
      return result
      
    } catch (error) {
      console.error('保存页面配置失败:', error)
      throw error
    }
  }

  /**
   * 清除缓存
   * @param {string} scenario - 场景名称（可选）
   * @param {string} pageType - 页面类型（可选）
   */
  clearCache(scenario = null, pageType = null) {
    if (scenario && pageType) {
      // 清除特定配置缓存
      const cacheKey = `${scenario}:${pageType}`
      this.cache.delete(cacheKey)
      console.log('清除特定配置缓存:', cacheKey)
    } else if (scenario) {
      // 清除特定场景的所有缓存
      for (const key of this.cache.keys()) {
        if (key.startsWith(`${scenario}:`)) {
          this.cache.delete(key)
        }
      }
      console.log('清除场景缓存:', scenario)
    } else {
      // 清除所有缓存
      this.cache.clear()
      console.log('清除所有配置缓存')
    }
  }

  /**
   * 获取缓存状态
   * @returns {Object} 缓存状态信息
   */
  getCacheStatus() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 验证页面配置
   * @param {Object} config - 页面配置
   * @returns {boolean} 配置是否有效
   */
  validatePageConfig(config) {
    if (!config) return false
    
    const requiredFields = ['id', 'name', 'path', 'components']
    return requiredFields.every(field => config.hasOwnProperty(field))
  }

  /**
   * 获取页面配置的URL
   * @param {string} scenario - 场景名称
   * @param {string} pageType - 页面类型
   * @returns {string} 页面URL
   */
  getPageUrl(scenario, pageType = 'list') {
    return `/low-code/${scenario}/${pageType}`
  }

  /**
   * 从URL解析页面信息
   * @param {string} url - 页面URL
   * @returns {Object|null} 页面信息 {scenario, pageType}
   */
  parsePageUrl(url) {
    // 支持多种URL格式：
    // /low-code/scenario -> {scenario: 'scenario', pageType: 'list'}
    // /low-code/scenario/pageType -> {scenario: 'scenario', pageType: 'pageType'}
    // /low-code/demousergroup/list -> {scenario: 'demousergroup', pageType: 'list'}

    // 移除开头的 /low-code/ 前缀
    const cleanPath = url.replace(/^\/low-code\//, '')

    if (!cleanPath) {
      return null
    }

    // 分割路径段
    const segments = cleanPath.split('/').filter(segment => segment.length > 0)

    if (segments.length === 0) {
      return null
    }

    // 第一个段是scenario，第二个段是pageType（默认为list）
    return {
      scenario: segments[0],
      pageType: segments[1] || 'list'
    }
  }
}

// 创建单例实例
export const pageConfigManager = new PageConfigManager() 