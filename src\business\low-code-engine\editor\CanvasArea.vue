<template>
  <div class="wysiwyg-editor">
    <!-- 顶部工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <div class="page-selector">
          <AppSelect :model-value="selectedPageName" :items="pageOptions" item-title="title" item-value="value"
            placeholder="Please select page name..." density="compact" variant="outlined" hide-details
            style="width: 200px;" @update:model-value="handlePageChange" />
        </div>
        <div class="save-status" :class="{ 'dirty': isDirty }">
          <VIcon :icon="saveStatusIcon" :color="saveStatusColor" size="12" class="mr-1" />
          {{ saveStatusText }}
        </div>
      </div>

      <div class="toolbar-center">
        <div class="view-controls">
          <VBtn :variant="viewMode === 'design' ? 'flat' : 'outlined'" size="small" @click="viewMode = 'design'">
            <VIcon icon="mdi-palette" size="16" class="mr-1" />
            Design
          </VBtn>
          <VBtn :variant="viewMode === 'preview' ? 'flat' : 'outlined'" size="small" @click="viewMode = 'preview'">
            <VIcon icon="mdi-eye" size="16" class="mr-1" />
            Preview
          </VBtn>
          <VBtn :variant="viewMode === 'code' ? 'flat' : 'outlined'" size="small" @click="viewMode = 'code'">
            <VIcon icon="mdi-code-tags" size="16" class="mr-1" />
            Code
          </VBtn>
        </div>
        <VDivider vertical class="mx-4" />

      </div>

      <div class="toolbar-right">
        <VBtn variant="outlined" size="small" @click="undo" :disabled="!canUndo">
          <VIcon icon="mdi-undo" size="16" />
        </VBtn>
        <VBtn variant="outlined" size="small" @click="redo" :disabled="!canRedo">
          <VIcon icon="mdi-redo" size="16" />
        </VBtn>
        <VDivider vertical />
        <VBtn color="success" variant="flat" size="small" @click="savePage">
          <VIcon icon="mdi-content-save" size="16" class="mr-1" />
          Save
        </VBtn>
        <VBtn color="primary" variant="flat" size="small" @click="newPage">
          <VIcon icon="mdi-plus" size="16" class="mr-1" />
          New
        </VBtn>
        <!-- <VBtn color="primary" variant="flat" size="small" @click="publishPage">
          <VIcon icon="mdi-rocket-launch" size="16" class="mr-1" />
          Publish
        </VBtn> -->
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 设计模式 -->
      <div v-if="viewMode === 'design'" class="design-mode">
        <div class="canvas-container" ref="canvasRef">
          <!-- 拖拽放置区域 -->
          <div class="dropzone" :class="{ 'editing': isEditing }" ref="dropzoneRef" @drop="handleDrop($event)"
            @dragover="handleDragOver($event)" @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)"
            @click="handleDropzoneClick">
            <!-- 空状态提示 -->
            <div v-if="isEmpty" class="empty-state">
              <VIcon icon="mdi-plus-circle-outline" size="48" color="#666" />
              <h3>Start designing your page</h3>
              <p>Drag components from the left component library here, or click the quick generation button</p>
              <div class="quick-actions">
                <VBtn variant="outlined" size="small" @click="generateSmartPage('list')">
                  <VIcon icon="mdi-format-list-bulleted" size="16" class="mr-1" />
                  Generate List Page
                </VBtn>
                <VBtn variant="outlined" size="small" @click="generateSmartPage('create')">
                  <VIcon icon="mdi-form-textbox" size="16" class="mr-1" />
                  Generate Create Page
                </VBtn>
              </div>
            </div>

            <!-- 页面组件 -->
            <div v-else class="page-components">
              <div v-if="pageComponents.length === 0" class="no-components">
                <p>No components detected, please check the data format</p>
                <details>
                  <summary>Debug Information</summary>
                  <pre>{{ { pageConfig: props.pageConfig, canvasContent: props.canvasContent } }}</pre>
                </details>
              </div>
              <EditableComponent v-for="(component, index) in pageComponents" :key="component.id || index"
                :component="component" :index="index" :is-selected="selectedComponentId === component.id"
                :context="renderContext" @select="selectComponent" @update="updateComponent" @delete="deleteComponent"
                @duplicate="duplicateComponent" />
            </div>
          </div>
        </div>
      </div>

      <!-- 预览模式 -->
      <div v-else-if="viewMode === 'preview'" class="preview-mode">
        <div class="preview-container">
          <div class="preview-content">
            <RenderNode :nodes="props.pageConfig?.components || []" :context="renderContext" />
          </div>
        </div>
      </div>

      <!-- 代码模式 -->
      <div v-else-if="viewMode === 'code'" class="code-mode">
        <div class="code-container">
          <div class="code-header">
            <h3>Page Configuration</h3>
            <VBtn variant="outlined" size="small" @click="copyCode">
              <VIcon icon="mdi-content-copy" size="16" class="mr-1" />
              Copy
            </VBtn>
          </div>
          <div class="code-content">
            <pre><code>{{ pageConfigJson }}</code></pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import EditableComponent from './EditableComponent.vue'
import RenderNode from '@/business/low-code-engine/editor/RenderNode.vue'
import { extractUsedVariables, createRenderContext } from '@/business/low-code-engine/renderer/utils'
// 已删除 htmlTemplates.js 相关import

// Props
const props = defineProps({
  selectedScenario: String,
  scenarios: Array,
  selectedPageType: String,
  hasContent: Boolean,
  canvasContent: String,
  pageConfig: Object
})

// Emits
const emit = defineEmits([
  'page-change',
  'save-page',
  'preview-page',
  'publish-page',
  'canvas-drop',
  'component-select',
  'component-update',
  'component-delete',
  'generate-page'
])

const pageNames = inject('pageNames')
const pageConfigs = inject('pageConfigs')

const selectedPageName = defineModel('selectedPageName', {
  type: String,
  default: null
})


const pageOptions = computed(() => {
  return pageNames.value.map(name => ({
    title: name,
    value: name
  }))
})

// 监听页面配置变化，自动更新画布组件
watch(() => props.pageConfig, (newConfig) => {
  if (newConfig && newConfig.components && newConfig.components.length > 0) {
    console.log('CanvasArea: 接收到新的页面配置，更新画布组件:', newConfig.components)
    console.log('CanvasArea: pageComponents 已通过 computed 自动更新:', pageComponents.value)
  } else {
    console.log('CanvasArea: 页面配置为空或没有组件')
  }
}, { deep: true })

// 🔥 flattenComponents函数已移除，直接使用原始组件数据

// 响应式数据
const viewMode = ref('design') // design, preview, code
const isDirty = defineModel('isDirty', {
  type: Boolean,
  default: true
})
const isEditing = ref(false)
const selectedComponentId = ref(null)

// 🔥 递归确保所有组件都有ID
function ensureComponentId(component, parentPath = '') {
  if (!component.id) {
    component.id = parentPath ? `${parentPath}-child-${Date.now()}` : `component-${Date.now()}`
  }

  if (component.children && Array.isArray(component.children)) {
    component.children.forEach((child, index) => {
      ensureComponentId(child, `${component.id}-${index}`)
    })
  }

  return component
}

// 🔥 核心修复：正确处理页面组件数据
const pageComponents = computed(() => {
  console.log('CanvasArea: 计算pageComponents')
  console.log('CanvasArea: props.pageConfig:', props.pageConfig)
  console.log('CanvasArea: props.canvasContent:', props.canvasContent)

  // 优先使用pageConfig.components
  if (props.pageConfig?.components && props.pageConfig.components.length > 0) {
    console.log('CanvasArea: 使用pageConfig.components:', props.pageConfig.components)
    // 🔥 修复：直接返回组件数组，不要扁平化处理
    const result = props.pageConfig.components.map((component, index) => {
      console.log(`🔧 CanvasArea: 处理组件 ${index}:`, component)
      console.log(`🔧 CanvasArea: 原始component.id:`, component.id)
      const newId = (component.id && component.id !== 'undefined' && component.id !== null) ? component.id : `component-${index}`
      console.log(`🔧 CanvasArea: 设置新ID:`, newId)

      const processedComponent = {
        ...component,
        id: newId,
        // 为编辑器添加一些元数据
        _isRootComponent: true,
        _editableStyle: {
          border: '1px dashed #666',
          margin: '8px',
          padding: '8px',
          borderRadius: '4px',
          minHeight: '50px'
        }
      }

      // 🔥 强制确保ID已正确设置
      if (!processedComponent.id) {
        processedComponent.id = `component-${index}`
        console.log(`🚨 CanvasArea: 强制设置ID为:`, processedComponent.id)
      }

      // 🔥 递归确保所有嵌套组件都有ID
      ensureComponentId(processedComponent)

      console.log(`🔧 CanvasArea: 处理后的组件:`, processedComponent)
      return processedComponent
    })

    console.log('🔧 CanvasArea: 最终返回的组件列表:', result)
    return result
  }

  // 如果pageConfig为空，尝试解析canvasContent
  if (props.canvasContent) {
    try {
      const parsed = JSON.parse(props.canvasContent)
      console.log('CanvasArea: 解析canvasContent:', parsed)

      if (parsed.components && parsed.components.length > 0) {
        console.log('CanvasArea: 使用canvasContent.components:', parsed.components)
        return parsed.components.map((component, index) => ({
          ...component,
          id: component.id || `component-${index}`,
          _isRootComponent: true,
          _editableStyle: {
            border: '1px dashed #666',
            margin: '8px',
            padding: '8px',
            borderRadius: '4px',
            minHeight: '50px'
          }
        }))
      }
    } catch (error) {
      console.error('CanvasArea: 解析canvasContent失败:', error)
    }
  }

  console.log('CanvasArea: 没有找到可用的组件数据，返回空数组')
  return []
})

const canvasRef = ref(null)
const dropzoneRef = ref(null)
// 已删除 renderFrame、renderFrameSrc、previewFrameSrc、onRenderFrameLoad、syncToRenderLayer等iframe相关内容

// 操作历史
const history = ref([])
const historyIndex = ref(-1)

// 页面类型选项
const pageTypes = [
  { label: 'List Page', value: 'list' },
  { label: 'Create Page', value: 'create' },
  { label: 'Edit Page', value: 'edit' },
  { label: 'Detail Page', value: 'detail' },
]

// 计算属性
const saveStatusIcon = computed(() => {
  return isDirty.value ? 'mdi-circle' : 'mdi-check-circle'
})

const saveStatusColor = computed(() => {
  return isDirty.value ? '#FFA726' : '#44D62C'
})

const saveStatusText = computed(() => {
  return isDirty.value ? 'Unsaved' : 'Saved'
})

const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

const isEdit = computed(() => {
  return selectedPageName.value !== null
})

// 🔥 修复：判断画布是否为空的逻辑
const isEmpty = computed(() => {
  const hasComponents = pageComponents.value && pageComponents.value.length > 0
  console.log('CanvasArea: isEmpty计算 - hasComponents:', hasComponents, 'pageComponents:', pageComponents.value)
  return !hasComponents
})

// 🔥 修复：创建渲染上下文，动态提取JSON中使用的变量
const renderContext = computed(() => {
  const pageData = props.pageConfig?.data || {}

  // 动态提取页面配置中使用的变量
  const usedVariables = extractUsedVariables(props.pageConfig || {})
  console.log('🔍 动态提取的变量:', Array.from(usedVariables))

  // 根据使用的变量创建上下文
  const contextData = createRenderContext(usedVariables, pageData)
  console.log('🔍 contextData构建完成，headers长度:', contextData.headers?.length || 0)

  // 🔥 关键验证：确认contextData.headers确实有数据
  if (contextData.headers && contextData.headers.length > 0) {
    console.log('✅ contextData.headers构建成功!', contextData.headers.slice(0, 2))
  } else {
    console.error('💥 contextData.headers构建失败!')
    console.error('💥 原始pageData.headers长度:', pageData.headers?.length || 0)
    console.error('💥 原始pageData.headers:', pageData.headers)
    console.error('💥 最终contextData.headers:', contextData.headers)
    console.error('💥 完整contextData:', contextData)
  }

  return contextData
})

const pageConfigJson = computed(() => {
  return JSON.stringify(props.pageConfig, null, 2)
})

// 🔥 监控pageConfig变化，调试headers传递问题
watch(() => props.pageConfig, (newConfig, oldConfig) => {
  console.log('🔥 CanvasArea: pageConfig发生变化!')
  console.log('🔥 CanvasArea: 新的pageConfig.data.headers长度:', newConfig?.data?.headers?.length || 0)
  console.log('🔥 CanvasArea: 新的pageConfig.data.headers:', newConfig?.data?.headers)
  console.log('🔥 CanvasArea: 完整的pageConfig.data:', newConfig?.data)
}, { immediate: true, deep: true })

// 方法
function handlePageChange(pageName) {
  selectedPageName.value = pageName
  isDirty.value = false
  emit('page-name-change', pageName)
}

function savePage() {
  emit('save-page', isEdit.value)
  isDirty.value = false
  addToHistory('save', { pageConfig: props.pageConfig })
}

function newPage() {
  isDirty.value = true
  selectedPageName.value = null
  emit('new-page')
}

function publishPage() {
  emit('publish-page')
}

function generateSmartPage(pageType) {
  emit('generate-page', pageType)
}

function selectComponent(componentId) {
  console.log('🔥 CanvasArea: selectComponent被调用, componentId:', componentId)
  selectedComponentId.value = componentId
  isEditing.value = true
  console.log('🔥 CanvasArea: 发出component-select事件, componentId:', componentId)
  emit('component-select', componentId)
}

function handleCanvasClick(event) {
  console.log("CanvasArea: handleCanvasClick", event.target === event.currentTarget, event.target, event.currentTarget)
  // 如果点击的是画布空白区域（不是组件），取消选择
  if (event.target === event.currentTarget) {
    selectedComponentId.value = null
    emit('component-select', null)
  }
}

function updateComponent(componentId, updates) {
  const index = pageComponents.value.findIndex(c => c.id === componentId)
  if (index !== -1) {
    const oldComponent = JSON.parse(JSON.stringify(pageComponents.value[index]))
    
    // 更新组件
    pageComponents.value[index] = { ...pageComponents.value[index], ...updates }
    
    // 如果是属性更新，记录完整的历史
    if (updates.oldComponent && updates.newComponent) {
      addToHistory('update', {
        componentId,
        oldComponent: updates.oldComponent,
        newComponent: pageComponents.value[index]
      })
    } else {
      // 其他类型的更新保持原有逻辑
      addToHistory('update', { componentId, updates })
    }
    
    isDirty.value = true
    emit('component-update', componentId, updates)

    // 同步到渲染层
    // syncToRenderLayer() // Removed
  }
}

function deleteComponent(componentId) {
  const index = pageComponents.value.findIndex(c => c.id === componentId)
  if (index !== -1) {
    const deleted = pageComponents.value.splice(index, 1)[0]
    isDirty.value = true
    addToHistory('delete', { componentId, component: deleted, index })
    emit('component-delete', componentId)

    if (selectedComponentId.value === componentId) {
      selectedComponentId.value = null
    }

    // 同步到渲染层
    // syncToRenderLayer() // Removed
  }
}

function duplicateComponent(componentId) {
  const component = pageComponents.value.find(c => c.id === componentId)
  if (component) {
    const duplicated = {
      ...component,
      id: generateId(),
      name: `${component.name} (副本)`
    }
    pageComponents.value.push(duplicated)
    isDirty.value = true
    addToHistory('duplicate', { originalId: componentId, newComponent: duplicated })

    // 同步到渲染层
    // syncToRenderLayer() // Removed
  }
}

function handleDragOver(event) {
  if (!event || typeof event.preventDefault !== 'function') {
    return
  }
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

function handleDragEnter(event) {
  if (!event || typeof event.preventDefault !== 'function') {
    return
  }
  event.preventDefault()
  const dropzone = document.querySelector('.dropzone')
  dropzone.classList.add('drag-over')
}

function handleDragLeave(event) {
  if (!event || typeof event.preventDefault !== 'function') {
    return
  }
  event.preventDefault()
  const dropzone = document.querySelector('.dropzone')
  dropzone.classList.remove('drag-over')
}

function handleDrop(event) {
  console.log('CanvasArea: 接收到拖拽事件', event)

  if (!event || typeof event.preventDefault !== 'function') {
    console.error('CanvasArea: 无效的事件对象', event)
    return
  }

  event.preventDefault()
  event.target.classList.remove('drag-over')

  try {
    if (!event.dataTransfer) {
      console.error('CanvasArea: 没有 dataTransfer 对象')
      return
    }

    const componentData = JSON.parse(event.dataTransfer.getData('component'))
    console.log('CanvasArea: 拖拽的组件数据:', componentData)

    // 计算拖拽位置
    const rect = dropzoneRef.value.getBoundingClientRect()
    const dropX = event.clientX - rect.left
    const dropY = event.clientY - rect.top

    console.log('CanvasArea: 拖拽位置:', { dropX, dropY })

    const defaultProps = getDefaultProps(componentData.type)
    const newComponent = {
      id: generateId(),
      type: componentData.type,
      name: componentData.label,
      props: {
        ...defaultProps,
        // 确保按钮有文本内容
        ...(componentData.type === 'VBtn' && { children: componentData.label })
      },
      children: [],
      style: {
        position: 'absolute',
        left: `${dropX}px`,
        top: `${dropY}px`,
        zIndex: pageComponents.value.length + 1
      },
      class: ''
    }

    console.log('CanvasArea: 创建新组件:', newComponent)
    pageComponents.value.push(newComponent)
    console.log('CanvasArea: 当前组件列表:', pageComponents.value)
    isDirty.value = true
    addToHistory('add', { component: newComponent })

    emit('canvas-drop', newComponent)
    console.log('CanvasArea: 组件已添加到画布')
  } catch (error) {
    console.error('CanvasArea: 拖拽处理失败:', error)
    console.error('CanvasArea: 错误详情:', error.message)
    console.error('CanvasArea: 事件对象:', event)
  }
}

function undo() {
  console.log('CanvasArea: undo', canUndo.value)
  if (canUndo.value) {
    historyIndex.value--
    const action = history.value[historyIndex.value]
    applyHistoryAction(action, true)
  }
}

function redo() {
  if (canRedo.value) {
    historyIndex.value++
    const action = history.value[historyIndex.value]
    applyHistoryAction(action, false)
  }
}

function addToHistory(type, data) {
  console.log('addToHistory', type, data)
  // 移除当前位置之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)

  // 添加新的历史记录
  history.value.push({
    type,
    data,
    timestamp: Date.now()
  })

  historyIndex.value = history.value.length - 1

  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

function applyHistoryAction(action, isUndo) {
  switch (action.type) {
    case 'add':
      if (isUndo) {
        deleteComponent(action.data.component.id)
      } else {
        pageComponents.value.push(action.data.component)
      }
      break
    case 'delete':
      if (isUndo) {
        pageComponents.value.splice(action.data.index, 0, action.data.component)
      } else {
        deleteComponent(action.data.componentId)
      }
      break
    case 'update':
      if (action.data.oldComponent && action.data.newComponent) {
        // 属性更新类型：有完整的旧状态和新状态
        if (isUndo) {
          // 撤销：恢复到旧状态
          const index = pageComponents.value.findIndex(c => c.id === action.data.componentId)
          if (index !== -1) {
            pageComponents.value[index] = action.data.oldComponent
          }
        } else {
          // 重做：恢复到新状态
          const index = pageComponents.value.findIndex(c => c.id === action.data.componentId)
          if (index !== -1) {
            pageComponents.value[index] = action.data.newComponent
          }
        }
      } else {
        // 其他类型的更新：使用原有的简单逻辑
        console.log('其他类型的更新操作:', action)
      }
      break
  }
}

function generateId() {
  return 'comp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}



function getDefaultProps(componentType) {
  const defaultProps = {
    VCard: {
      class: 'pa-4',
      elevation: 2,
      style: 'min-height: 100px; min-width: 200px;'
    },
    VBtn: {
      color: 'primary',
      variant: 'flat',
      style: 'min-width: 80px; min-height: 36px;'
    },
    VTextField: {
      label: '输入框',
      variant: 'outlined',
      style: 'min-width: 200px;'
    },
    VSelect: {
      label: '选择器',
      variant: 'outlined',
      style: 'min-width: 200px;'
    },
    VDataTable: {
      headers: [],
      items: [],
      style: 'min-width: 400px; min-height: 200px;'
    }
  }

  return defaultProps[componentType] || {}
}

function openPreviewInNewTab() {
  emit('preview-page')
}

function copyCode() {
  navigator.clipboard.writeText(pageConfigJson.value)
}

function handleDropzoneClick(event) {
  // 如果点击的是空白区域，进入编辑模式
  if (event.target === event.currentTarget || event.target.classList.contains('dropzone')) {
    startEditing()
    selectedComponentId.value = null
    emit('component-select', null)
  }
}

function startEditing() {
  isEditing.value = true
  console.log('进入编辑模式')
}

function stopEditing() {
  isEditing.value = false
  selectedComponentId.value = null
  console.log('退出编辑模式')
}

// 渲染层相关方法
// 已删除 renderFrameSrc、previewFrameSrc、onRenderFrameLoad、syncToRenderLayer等iframe相关内容


// 初始化设置
onMounted(async () => {
  await nextTick()
  console.log('CanvasArea 初始化完成')

  // 添加ESC键监听
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && isEditing.value) {
      stopEditing()
    }
  })
})

// 监听页面配置变化
watch(() => props.pageConfig, (newConfig) => {
  if (newConfig && newConfig.components) {
    pageComponents.value = newConfig.components
  }
}, { deep: true })

// 监听画布内容变化
// 🔥 修复：监听canvasContent变化，触发pageComponents重新计算
watch(() => props.canvasContent, (newContent) => {
  if (newContent) {
    console.log('CanvasArea: canvasContent发生变化:', newContent)
    // pageComponents会自动重新计算，因为它依赖props.canvasContent
    nextTick(() => {
      console.log('CanvasArea: 更新后的pageComponents:', pageComponents.value)
    })
  }
})

// 监听组件变化，自动同步到渲染层
watch(pageComponents, () => {
  // syncToRenderLayer() // Removed
}, { deep: true })


</script>

<style scoped>
.wysiwyg-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  height: 100%;
  min-width: 0;
  /* 防止flex item过宽 */
  overflow: hidden;
  /* 让子元素处理滚动 */
}

.editor-toolbar {
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  padding: 12px 16px;
  display: flex;
  gap: 16px;
  align-items: center;
  min-height: 56px;
  overflow: auto;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-center {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-selector {
  min-width: 200px;
}

.save-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #44D62C;
  transition: all 0.2s ease;
}

.save-status.dirty {
  color: #FFA726;
}

.view-controls {
  display: flex;
  gap: 4px;
}



.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.design-mode {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: auto;
  /* 修改为可滚动 */
  background: #1a1a1a;
  min-height: 0;
  /* 防止flex item过高 */
}



.dropzone {
  min-height: 100%;
  position: relative;
  background: #2d2d2d;
  border: 2px dashed #404040;
  padding: 20px;
  /* 添加内边距 */
  border-radius: 8px;
  transition: all 0.2s ease;
  padding: 10px;
}

.dropzone.drop-active,
.dropzone.drag-over {
  border-color: #44D62C;
  border-style: dashed;
  background: rgba(68, 214, 44, 0.05);
  box-shadow: 0 0 10px rgba(68, 214, 44, 0.2);
}

/* 编辑状态的高亮边框 */
.dropzone.editing {
  border-color: #44D62C;
  border-style: dashed;
  border-width: 2px;
  box-shadow: 0 0 15px rgba(68, 214, 44, 0.3);
  animation: editing-pulse 2s infinite;

}

@keyframes editing-pulse {

  0%,
  100% {
    box-shadow: 0 0 15px rgba(68, 214, 44, 0.3);
  }

  50% {
    box-shadow: 0 0 25px rgba(68, 214, 44, 0.5);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
  text-align: center;
}

.empty-state h3 {
  margin: 16px 0 8px 0;
  color: #ccc;
  font-size: 18px;
}

.empty-state p {
  margin: 0 0 24px 0;
  color: #999;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.page-components {
  min-height: 100%;
  position: relative;
  width: 100%;
  height: 100%;
}

/* 渲染层样式 - 与编辑区保持一致 */
/* 已删除 renderFrameSrc、previewFrameSrc、onRenderFrameLoad、syncToRenderLayer等iframe相关内容 */

/* 预览模式 */
.preview-mode {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #000000;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.preview-header {
  background: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.preview-content {
  flex: 1;
  overflow: hidden;
}

/* 代码模式 */
.code-mode {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
}

.code-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.code-header {
  background: #2d2d2d;
  padding: 12px 16px;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-header h3 {
  margin: 0;
  color: #fff;
  font-size: 16px;
}

.code-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
  max-height: calc(100vh - 200px);
  min-height: 400px;
}

.code-content pre {
  margin: 0;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
}


/* 渲染层样式 */
/* 已删除 renderFrameSrc、previewFrameSrc、onRenderFrameLoad、syncToRenderLayer等iframe相关内容 */

/* 滚动条样式 */
.canvas-container::-webkit-scrollbar,
.preview-content::-webkit-scrollbar,
.code-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.canvas-container::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track,
.code-content::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.canvas-container::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb,
.code-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.canvas-container::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover,
.code-content::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 调试信息样式 */
.no-components {
  padding: 20px;
  text-align: center;
  color: #999;
  border: 1px dashed #444;
  border-radius: 8px;
  margin: 20px;
}

.no-components details {
  margin-top: 16px;
  text-align: left;
}

.no-components pre {
  background: #2d2d2d;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow: auto;
  max-height: 200px;
  color: #ccc;
}
</style>