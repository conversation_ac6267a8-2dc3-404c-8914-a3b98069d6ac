<script setup>
defineOptions({
  name: 'AppTextField',
  inheritAttrs: false,
})

const props = defineProps({
  type: {
    type: String,
    default: 'text'
  },
  min: {
    type: [Number, String],
    default: undefined
  },
  max: {
    type: [Number, String],
    default: undefined
  },
  decimalPlaces: {
    type: [Number, String],
    default: undefined
  },
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:model-value', 'blur'])

const elementId = computed(() => {
  const attrs = useAttrs()
  const _elementIdToken = attrs.id || attrs.label
  
  return _elementIdToken ? `app-text-field-${ _elementIdToken }-${ Math.random().toString(36).slice(2, 7) }` : undefined
})

const attrs = useAttrs()
const required = computed(() => attrs.hasOwnProperty('required'))

const label = computed(() => {
  if (required.value) {
    return attrs.label + '*'
  }
  return attrs.label
})

const checkValue = (value) => {
  if (value === '' || value === '-') return true
  
  const numValue = parseFloat(value)
  if (isNaN(numValue)) return false
  
  // Check maximum value
  if (props.max !== undefined && numValue > parseFloat(props.max)) {
    return false
  }
  
  // Check minimum value
  if (props.min !== undefined && numValue < parseFloat(props.min)) {
    return false
  }
  
  return true
}

const handleKeydown = (event) => {
  if (props.type !== 'number') return

  const key = event.key
  const value = event.target.value
  const selectionStart = event.target.selectionStart
  const selectionEnd = event.target.selectionEnd
  const parts = value.split('.')
  const hasDecimal = parts.length > 1
  const decimalPlaces = hasDecimal ? parts[1].length : 0

  // Allowed control keys
  if (['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(key)) {
    return
  }

  // Handle number input
  if (/^\d$/.test(key)) {
    // Check if the value after input is valid
    const beforeCursor = value.substring(0, selectionStart)
    const afterCursor = value.substring(selectionEnd)
    const newValue = beforeCursor + key + afterCursor

    // Handle leading zero cases
    const isFirstChar = selectionStart === 0
    const hasLeadingZero = beforeCursor.match(/^-?0\d/)
    const isZeroInput = key === '0'
    
    // Allow if it's first character and input is 0
    if (isFirstChar && isZeroInput) {
      return
    }
    
    // If there's already a leading zero and not inputting after decimal point
    if (hasLeadingZero && !hasDecimal) {
      event.preventDefault()
      return
    }
    
    // If current value is 0 and not inputting decimal point
    if (beforeCursor === '0' && !hasDecimal && key !== '.') {
      event.preventDefault()
      return
    }

    if (!checkValue(newValue)) {
      event.preventDefault()
      return
    }

    // If inputting after decimal point
    if (hasDecimal && selectionStart > value.indexOf('.')) {
      // Prevent input if decimal places limit reached
      if (props.decimalPlaces !== undefined && 
          (decimalPlaces >= Number(props.decimalPlaces) || 
           (selectionStart === selectionEnd && decimalPlaces >= Number(props.decimalPlaces)))) {
        event.preventDefault()
        return
      }
    }
    return
  }

  // Handle decimal point
  if (key === '.') {
    // Prevent input if decimal point already exists
    if (hasDecimal) {
      event.preventDefault()
      return
    }
    // Auto add 0 if no numbers yet
    if (!value) {
      emit('update:model-value', '0.')
      event.preventDefault()
      return
    }
    return
  }

  // Handle minus sign
  if (key === '-' && props.min !== undefined && parseFloat(props.min) < 0) {
    // Only allow minus sign at start
    if (selectionStart !== 0 || value.includes('-')) {
      event.preventDefault()
      return
    }
    return
  }

  // Prevent all other inputs
  event.preventDefault()
}

const handleUpdate = (value) => {
  if (props.type !== 'number') {
    emit('update:model-value', value)
    return
  }

  // Only allow numbers, decimal point and minus sign
  let newValue = value.replace(/[^\d.-]/g, '')
  
  // Ensure single decimal point and correct minus sign position
  const parts = newValue.split('.')
  if (parts.length > 2) {
    newValue = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // Handle minus sign
  if (newValue.includes('-')) {
    if (newValue.indexOf('-') !== 0) {
      newValue = newValue.replace(/-/g, '')
    } else {
      newValue = '-' + newValue.substring(1).replace(/-/g, '')
    }
  }
  
  // Handle leading zeros
  if (!newValue.includes('.')) {
    newValue = newValue.replace(/^(-)?0+(\d)/, '$1$2')
  } else {
    newValue = newValue.replace(/^(-)?0+(\d+\.)/, '$1$2')
  }
  
  // Handle decimal places limit
  if (props.decimalPlaces !== undefined && parts.length === 2) {
    newValue = parts[0] + '.' + parts[1].slice(0, Number(props.decimalPlaces))
  }
  
  // Handle min/max limits
  const numValue = parseFloat(newValue)
  if (!isNaN(numValue)) {
    if (props.min !== undefined && numValue < parseFloat(props.min)) {
      newValue = props.min.toString()
    }
    if (props.max !== undefined && numValue > parseFloat(props.max)) {
      newValue = props.max.toString()
    }
  }
  
  emit('update:model-value', newValue)
}

const handleBlur = (event) => {
  const value = event.target.value
  
  // Set to min value if empty and has min limit
  if ((!value || value === '-') && props.min !== undefined) {
    emit('update:model-value', props.min.toString())
    return
  }
  
  // If only 0 input and has min limit
  if (value === '0' && props.min !== undefined && parseFloat(props.min) > 0) {
    emit('update:model-value', props.min.toString())
    return
  }
  
  emit('blur', event)
}
</script>

<template>
  <div
    class="app-text-field flex-grow-1"
    :class="$attrs.class"
  >
    <VLabel
      v-if="label"
      :for="elementId"
      class="mb-1 text-body-2 text-wrap leading-20px"
      :text="label"
    />
    <VTextField
      v-bind="{
        ...$attrs,
        class: null,
        label: undefined,
        variant: 'outlined',
        id: elementId,
        clearIcon: 'tabler-x',
        type: 'text',
        modelValue: props.modelValue,
        'onUpdate:model-value': handleUpdate,
        onKeydown: handleKeydown,
        onBlur: handleBlur,
      }"
    >
      <template
        v-for="(_, name) in $slots"
        :key="name"
        #[name]="slotProps"
      >
        <slot
          :name="name"
          v-bind="slotProps || {}"
        />
      </template>
    </VTextField>
  </div>
</template>
