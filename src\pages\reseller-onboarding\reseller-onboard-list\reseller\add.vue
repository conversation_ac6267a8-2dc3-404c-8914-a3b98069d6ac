<script setup>
import { useRouter } from "vue-router";
import CreateResellerForm from "@/business/reseller-onboarding/reseller-onboarding-list/createResellerForm.vue";
import { isEmpty } from '@/utils/helpers'
definePage({
  meta: {
    navActiveLink: "create-reseller",
    breadcrumb: "Create New Reseller Account",
  },
});

const router = useRouter();
const createResellerFormRef = ref(null);
const isSaving = ref(false);

// form data
const formModel = ref({
  resellerType: null,
  lastName: null,
  firstName: null,
  resellerEmail: null,
  phoneCountryCode: null,
  phoneNo: null,
  regName: null,
  website: null,
  regCountry: null,
  mainDistributionCountry: null,
  otherDistributionCountry: null,
  expectedSalesPerMonth: null,
  requireKybFlag: null,
  noRequireKybReason: null,
});

const handleCancel = () => {
  createResellerFormRef.value?.resetForm();
  router.back();
};

const handleSave = async () => {
  if (isSaving.value) return;
  // validate form
  const { valid, errors } = await createResellerFormRef.value?.validate();

  if (!valid) return;
  isSaving.value = true;
  try {
    const params = createResellerFormRef.value?.getParams();

    const resp = await $api("/api/reseller-onboard/v1", {
      method: "POST",
      data: params,
      showToast: false
    });
    const errorCode = resp?.errorCode;
    if (errorCode === "000000") {
      message.success("Reseller created successfully");
    } else {
      message.error(resp?.message || "Failed to create new reseller");
    }
    
    router.back();
  } catch (error) {
    console.error(error);
    message.error("Failed to create new reseller");
  } finally {
    isSaving.value = false;
  }
};
</script>

<template>
  <VCard>
    <CreateResellerForm ref="createResellerFormRef" v-model="formModel" type="add" />

    <VRow class="d-flex mt-3" style="width: 66vw;">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click.stop="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" data-testid="save-button" @click.stop="handleSave">
          CREATE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
