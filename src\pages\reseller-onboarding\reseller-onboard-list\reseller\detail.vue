<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router'
import { useEnquiryDetail } from '@/business/reseller-onboarding/reseller-onboarding-list/useEnquiryDetail'
import { isEmpty } from '@/utils/helpers';
import {
  requiredValidator,
  lengthLimitationValidator,
} from "@/utils/validators";
import { onMounted, ref } from 'vue';
definePage({
  meta: {
    navActiveLink: 'reseller-detail',
    breadcrumb: 'Reseller Details',
  },
})

const router = useRouter();
const isSaving = ref(false);
const route = useRoute();
const cacheParams = localStorage.getItem('reseller-onboard-enquiry-detail');
const params = cacheParams ? JSON.parse(cacheParams) : { resellerOnboardId: null, operationBtnFlag: false };
const {enquiryInfo, errorFlag} = useEnquiryDetail(params.resellerOnboardId);
const convertStringToBoolean = (flag) => {
  if (flag === 'true') {
    return true;
  } else {
    return false;
  }
};
//*********************************************** define constant variables ***********************************************
const OPERATION_TYPE = {
  APPROVE: 3,
  REJECT: 2,
};

const REJECT_REASONS = [
  "1. Incomplete Information‌",
  "2. False Information‌",
  "3. Duplicate Record‌",
  "4. Eligibility Criteria Not Met‌",
  "5. Strategic Misalignment‌",
  "6. Competitor/Conflict of Interest‌‌",
  "7. Non-Compliance‌‌",
  "8. Non-Responsive‌‌",
];


//*********************************************** confirm approval handle function ***********************************************
const selectedReason = ref(null);
const remarks = ref(null);
const isInProgress = ref(false);
const isInValid = ref(false);

const handleOperationClick = (type, enquiryInfo) => {
  selectedReason.value = null;
  remarks.value = null;

  const textContent =
    type === OPERATION_TYPE.APPROVE
      ? <div>Once approved, the reseller will gain access to the Reseller Console and can begin their KYB onboarding process. <br/>This action cannot be undone.</div>
      : type === OPERATION_TYPE.REJECT
        ? <div>Once rejected, the reseller will no longer be able to proceed unless a new enquiry is submitted. <br/>This action cannot be undone.</div>
        : <div></div>;

  const toastMsg =
    type === OPERATION_TYPE.APPROVE
      ? "Reseller Enquiry is successfully approved"
      : type === OPERATION_TYPE.REJECT
        ? "Reseller Enquiry is successfully rejected"
        : "";

  const width =
    type === OPERATION_TYPE.APPROVE
      ? 400
      : type === OPERATION_TYPE.REJECT
        ? 600
        : 400;

  confirmDialog({
    title: `${type === OPERATION_TYPE.APPROVE
      ? "Approve Reseller Enquiry?"
      : type === OPERATION_TYPE.REJECT
        ? "Reject Reseller Enquiry?"
        : ""
      }`,
    text:textContent,
    width,
    render: () => {
      return (
        <div>
          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.REJECT ? "block" : "none",
            }}
          >
            <AppSelect
              items={REJECT_REASONS}
              label="Reject Reason*"
              placeholder="Select a reason"
              v-model={selectedReason.value}
              item-text="text"
              item-value="value"
              rules={[requiredValidator]}
            />
            <AppTextField
              v-model={remarks.value}
              label="Remarks (Optional)"
              placeholder="Type a reason"
              rules={[(value) => lengthLimitationValidator(value, 250, "Remarks")]}
            />
          </div>
        </div>
      );
    },
    confirmButtonText: `${type === OPERATION_TYPE.APPROVE
      ? "APPROVE"
      : type === OPERATION_TYPE.REJECT
        ? "REJECT"
        : ""
      }`,
    confirmButtonColor: `${type === OPERATION_TYPE.APPROVE
      ? "#44D62C"
      : type === OPERATION_TYPE.REJECT
        ? "#FD4949"
        : ""
      }`,
    confirmButtonTextColor: "#000000",
    cancelButtonText: "CANCEL",
    onCancel: () => {
      isInProgress.value = false;
      isInValid.value = false
    },
    async onConfirm(close) {
      if (isInProgress.value) return;
      isInProgress.value = true;
      isInValid.value = false;

      if (type === OPERATION_TYPE.REJECT) {
        if (isEmpty(selectedReason.value)) {
          isInProgress.value = false;
          message.error("Must select Reject Reason");
          return;
        }

        if (remarks.value && remarks.value.length > 250) {
          isInProgress.value = false;
          message.error(
            "The Remark field length must be less than 250 characters"
          );
          return;
        }
      }

      if (isInValid.value) {
        isInProgress.value = false;
        return;
      }
      try {
        await $api("/api/reseller-onboard/v1/status", {
          method: "POST",
          data: {
            onboardId: enquiryInfo.onboardId,
            email: enquiryInfo.email,
            name: `${enquiryInfo.lastName} ${enquiryInfo.firstName}`,
            statusCode: type,
            reason:
              type === OPERATION_TYPE.REJECT
                ? JSON.stringify([{
                  reviewSectionCode: 0,
                  reviewReasonCode: REJECT_REASONS.indexOf(selectedReason.value) + 1,
                }])
                : null,
            remark: (type === OPERATION_TYPE.REJECT) ? remarks.value : null,
            description:
              type === OPERATION_TYPE.REJECT
                ? "enquiry reject"
                : "enquiry approve",
            version: enquiryInfo.version,
          },
          showToast: false
        });
        message.success(toastMsg);
        close();
        router.back();
      } catch (error) {
        console.error(error);
        message.error("Failed to update status");
      } finally {
        isInProgress.value = false;
      }
    },
  });
};


</script>

<template>
  <VCard style="width: 60vw;">
    <VCardItem class="pb-4 px-0">
      <VCardTitle>
        Reseller Details
      </VCardTitle>
    </VCardItem>

    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5">
        <div class="form-wrapper">
          <VCardText>
              <VRow>
                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Reseller Name</div>
                  <div class="detail-content">{{ enquiryInfo.lastName }} {{ enquiryInfo.firstName }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Email</div>
                  <div class="detail-content">{{ enquiryInfo.email }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Job Title</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.designation) ? "-" : enquiryInfo.designation }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Contact Number</div>
                  <div class="detail-content">{{isEmpty(enquiryInfo.phoneCountryCode) ? "-" : `+${enquiryInfo.phoneCountryCode} ${enquiryInfo.phoneNo}`}}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Company Name</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.regName) ? "-" : enquiryInfo.regName }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Website</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.website) ? "-" : enquiryInfo.website }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Registered Country</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.regCountryContent) ? "-" : enquiryInfo.regCountryContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Company’s Total Annual Revenue</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.regAnnualRevenueContent) ? "-" : enquiryInfo.regAnnualRevenueContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Reseller Type</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.resellerTypeContent) ? "-" : enquiryInfo.resellerTypeContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Main Distribution Country</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.mainDistributionCountryContent) ? "-" : enquiryInfo.mainDistributionCountryContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Other Distribution Country (Optional)</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.otherDistributionCountryContent) ? "-" : enquiryInfo.otherDistributionCountryContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Locations / Channels of Distribution</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.distributionChannelsContent) ? "-" : enquiryInfo.distributionChannelsContent }}{{ isEmpty(enquiryInfo.otherDistributionChannelsContent) ? '' : `, Others - ${enquiryInfo.otherDistributionChannelsContent}` }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Expected sales volume per month</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.expectedSalesPerMonthContent) ? "-" : enquiryInfo.expectedSalesPerMonthContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Partnership Type</div>
                  <div class="detail-content">{{ isEmpty(enquiryInfo.typeOfPartnerContent) ? "-" : enquiryInfo.typeOfPartnerContent }}</div>
                </VCol>

                <VCol cols="12" style="padding: 4px 24px;">
                  <div class="detail-title">Message</div>
                  <div class="detail-message">{{ isEmpty(enquiryInfo.message) ? "-" : enquiryInfo.message }}</div>
                </VCol>
              </VRow>
          </VCardText>
        </div>
      </VCol>
    </VRow>

    <VRow class="d-flex mt-3" v-if="params.operationBtnFlag">
      <VCol cols="12" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" :disabled="errorFlag" @click.stop="handleOperationClick(OPERATION_TYPE.REJECT, enquiryInfo)">
          REJECT
        </VBtn>
        <VBtn color="primary" :disabled="errorFlag" @click.stop="handleOperationClick(OPERATION_TYPE.APPROVE, enquiryInfo)" class="ml-2" data-testid="approve-button">
          APPROVE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 60vw;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
  padding: 0px 0px;
}

// :deep(.v-col-12) {
//   padding: 8px 12px;
// }

.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}

.detail-title {
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0.25px;
  text-align: left;
  color: #BBBBBB;
  opacity: 0.5;
}

.detail-content {
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: left;
  color: #BBBBBB;
  margin-top: 4px;
  opacity: 0.8;
}

.detail-message {
  font-family: 'Roboto';
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: left;
  color: #BBBBBB;
  background-color: #333333;
  border-radius: 4px;
  opacity: 0.8;
  padding: 12px 12px;
  margin-top: 4px;
  max-height: 150px;
  overflow-y: auto;
}
</style>
