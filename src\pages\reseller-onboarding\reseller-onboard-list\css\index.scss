.actions-grid {
  display: grid;
  grid-template-columns: 40px 40px;
  gap: 8px;
}

.action-item {
  display: flex;
  justify-content: center;
}

:deep(.v-data-table) {
  .v-table__wrapper {
    overflow: auto;
  }

  tr:has(.tabler-chevron-up) .v-data-table-column--last-fixed {
    border-bottom: none;
  }

  .v-data-table-column--last-fixed {
    right: 0;
    border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}

.pdf-box {
  position: fixed;
  z-index: 2000;
  border: 1px solid #111111;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease-out;
  will-change: transform;
  width: 40vw;
  height: 90vh;
  transform: translate(0px, 5px);
  overflow: auto;
  padding-right: 2px;
}

.file-box {
  position: fixed;
  z-index: 2001;
  border: 1px solid #111111;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease-out;
  will-change: transform;
  width: 35vw;
  height: 90vh;
  transform: translate(0px, 5px);
  overflow: auto;
  padding-right: 2px;
}

.pdf-drag-handle {
  padding: 16px 0px;
  background: #222222;
}

.file-drag-handle {
  padding: 16px 0px;
  background: #222222;
}

.file-title {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pdf-content {
  padding: 16px 16px;
  height: 100%;
  overflow: auto;
  border: 1px solid #111111;
  background: #111111;
}

.pdf-footer {
  padding: 36px 16px 36px 16px;
  border: 1px solid #222222;
  background: #222222;
}

.pdf-footer-plus {
  padding: 42px 16px 42px 16px;
  border: 1px solid #222222;
  background: #222222;
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
}

.pdf-viewer {
  height: 71vh;
  padding: 16px 16px;
}

.file-viewer {
  height: 79vh;
  padding: 8px 8px;
}

.img-file-viewer {
  width: -webkit-fill-available;
  height: -webkit-fill-available;
}

/* disable print */
@media print {
  body * {
    visibility: hidden !important;
  }
}

/* disable print */
@media print {
  .no-print {
    display: none !important;
  }
}

.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

.form-container {
  width: 100%;
  margin-bottom: 20px;
}

.row {
  display: flex;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.field {
  flex: 1;
  margin-right: 10px;
}

.field input {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

.actions {
  width: 100px;
  display: flex;
  align-items: center;
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

button {
  padding: 8px 12px;
  margin-right: 10px;
  cursor: pointer;
}

.review-btn {
  color: #000000 !important;
}

.panel-a {
  width: 40vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.panel-b {
  width: 35vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.review-trash-can {
  width: 24px;
  height: 24px;
  fill: #999999;
}

.review-trash-can:hover {
  width: 24px;
  height: 24px;
  fill: #ffffff;
}

.review-trash-can:active {
  width: 24px;
  height: 24px;
  fill: #44d62c;
}

canvas {
  margin-bottom: 10px;
}

.truncate-content {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}