/**
 * 后端组件类型到前端组件的映射配置
 */
export const COMPONENT_TYPE_MAPPING = {
  // 基础文本类型
  TEXT: {
    list: "span",
    form: "AppTextField",
    search: "AppTextField",
    detail: "span",
    props: {
      search: {
        placeholder: "Enter text to search",
      },
    },
  },

  // 数字类型
  NUMBER: {
    list: "span",
    form: "AppTextField",
    search: "AppTextField",
    detail: "span",
    props: {
      form: { type: "number" },
      search: {
        type: "number",
        placeholder: "Enter number",
      },
    },
  },

  // 选择器类型
  SELECT: {
    list: "span",
    form: "AppSelect",
    search: "AppSelect",
    detail: "span",
    props: {
      search: {
        placeholder: "Select an option",
      },
    },
  },

  // 日期时间类型
  DATETIME: {
    list: "span",
    form: "AppDateTimePicker",
    search: "AppDateTimePicker",
    detail: "span",
  },

  // 文本区域
  TEXTAREA: {
    list: "div",
    form: "VTextarea",
    search: "AppTextField",
    detail: "div",
  },

  // 邮箱
  EMAIL: {
    list: "span",
    form: "AppTextField",
    search: "AppTextField",
    detail: "span",
    props: {
      form: { type: "email" },
      search: { type: "email" },
    },
  },

  // 密码
  PASSWORD: {
    list: "****",
    form: "AppTextField",
    search: null, // 密码字段不支持搜索
    detail: "****",
    props: {
      form: { type: "password" },
    },
  },

  // SWITCH
  SWITCH: {
    list: "VSwitch",
    form: "VSwitch",
    search: "AppSelect",
    detail: "VChip",
    props: {
      list: {
        color: "primary",
        "true-value": "1",
        "false-value": "0"
      },
      search: {
        items: [
          { title: "Enabled", value: "1" },
          { title: "Disabled", value: "0" },
        ],
        clearable: true,
        placeholder: "Please select status",
      },
      form: {
        color: "primary",
        "true-value": "1",
        "false-value": "0",
      },
      detail: {
        color: '{{detailData.{{fieldName}} === "1" ? "success" : "error"}}',
        text: '{{detailData.{{fieldName}} === "1" ? "Enabled" : "Disabled"}}',
      },
    },
  },
};

/**
 * 根据后端字段定义生成前端组件配置
 * @param {Object} field - 后端字段定义
 * @param {string} pageType - 页面类型: 'list' | 'form' | 'search' | 'detail'
 * @returns {Object} 前端组件配置
 */
export function mapFieldToComponent(field, pageType, options = {}) {
  const componentType = field.layout?.component || "TEXT";
  const mapping = COMPONENT_TYPE_MAPPING[componentType];

  if (!mapping) {
    console.warn(`未找到组件类型 ${componentType} 的映射配置`);
    return COMPONENT_TYPE_MAPPING.TEXT;
  }

  const component = mapping[pageType];
  if (!component) {
    return null; // 某些字段在特定页面类型中不显示
  }

  // 基础配置
  const config = {
    type: component,
    props: {
      // 基础属性
      ...(mapping.props?.[pageType] || {}),
      // 动态替换字段名
      ...Object.fromEntries(
        Object.entries(mapping.props?.[pageType] || {}).map(([key, value]) => [
          key,
          typeof value === "string"
            ? value.replace(/\{\{fieldName\}\}/g, field.name)
            : value,
        ])
      ),
    },
  };

  // 根据页面类型添加特定配置
  switch (pageType) {
    case "list":
      return {
        ...config,
        props: {
          ...config.props,
          text: `{{item.${field.name}}}`,
          ...(field.frontend?.listProps || {}),
        },
      };

    case "form":
      return {
        ...config,
        model: `{{formData.${field.name}}}`,
        props: {
          ...config.props,
          label: field.displayName,
          placeholder: `Please input ${field.displayName}`,
          required: field.required,
          ...(field.frontend?.formProps || {}),
        },
        events: {
          "update:modelValue": [
            { action: "updateFormData", params: [field.name, "$event"] },
          ],
        },
      };

    case "search":
      return {
        ...config,
        model: `{{searchQuery.${field.name}}}`,
        props: {
          ...config.props,
          // 根据 hideLabels 选项决定是否显示标签
          ...(options.hideLabels ? {} : { label: field.displayName }),
          // 如果组件配置中没有placeholder，则使用默认的
          placeholder: config.props?.placeholder || `搜索${field.displayName}`,
          clearable: true,
          ...(field.frontend?.searchProps || {}),
        },
        events: {
          "update:modelValue": [{ action: "handleSearch" }],
        },
      };

    case "detail":
      return {
        ...config,
        props: {
          ...config.props,
          text: `{{detailData.${field.name}}}`,
          ...(field.frontend?.detailProps || {}),
        },
      };

    default:
      return config;
  }
}

/**
 * 生成表格头部配置
 * @param {Array} fields - 字段定义数组
 * @returns {Array} 表格头部配置
 */
export function generateTableHeaders(fields) {
  console.log("generateTableHeaders: 输入的fields:", fields);

  const headers = [
    {
      title: "No",
      key: "no",
      sortable: false,
      width: 70,
    },
  ];

  if (!fields || !Array.isArray(fields)) {
    console.error("generateTableHeaders: fields不是有效数组:", fields);
    return headers;
  }

  fields.forEach((field) => {
    console.log("generateTableHeaders: 处理字段:", field);
    if (field.frontend?.showInList !== false) {
      const header = {
        title: field.displayName || field.name,
        key: field.name,
        sortable: field.frontend?.sortable !== false,
        width: field.frontend?.columnWidth || "auto",
      };
      console.log("generateTableHeaders: 添加表头:", header);
      headers.push(header);
    }
  });

  // 添加操作列
  headers.push({
    title: "Actions",
    key: "actions",
    sortable: false,
    fixed: true,
    width: 120,
  });

  console.log("generateTableHeaders: 最终生成的headers:", headers);
  return headers;
}
