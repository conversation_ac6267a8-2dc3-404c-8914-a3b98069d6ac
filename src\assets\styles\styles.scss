// Write your overrides
:deep(.v-card) {
  background-color: #000000 !important;
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 6px; // 滚动条宽度
  height: 6px; // 滚动条高度
}

::-webkit-scrollbar-track {
  background: transparent; // 轨道背景透明
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2); // 滚动条颜色
  border-radius: 3px; // 圆角

  &:hover {
    background: rgba(255, 255, 255, 0.3); // 悬停时的颜色
  }
}

// 当滚动条出现时，保持内容不被挤压
::-webkit-scrollbar-track-piece {
  background: transparent;
}

// 滚动条角落的样式
::-webkit-scrollbar-corner {
  background: transparent;
}

// 自定义对话框样式
.v-card-custom {
  padding: 8px !important;
  background-color: #1e1e1e !important;

  .v-dialog-close-btn {
    right: 16px !important;
    top: 16px !important;
    background-color: #1e1e1e !important;
  }
}

.v-switch__track {
  border: 1px solid #4f4f4f;
  background-color: #707070 !important;
  &.bg-primary {
    border: 1px solid #30961f;
    background-color: #44d62c !important;
  }
}

.v-switch--inset .v-switch__thumb {
  background-color: #000000 !important;
}

.v-pagination__item.v-pagination__item--is-active {
  .v-btn.v-theme--dark.v-btn--disabled {
    --v-activated-opacity: 1 !important;
    opacity: 1 !important;
  }
  .v-btn__content {
    color: #000000 !important;
  }
}

// notification btn style
.plugin-web-update-notice-content {
  background: #1e1e1e !important;
  color: #ffffff !important;
}

.plugin-web-update-notice-refresh-btn {
  color: rgb(var(--v-theme-primary)) !important;
}

.plugin-web-update-notice-dismiss-btn {
  color: #eeeeee !important;
}

// table header style
.v-table__wrapper .v-data-table__thead {
  background-color: #111111 !important; // 设置表头背景色
}

.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
  border-bottom: none !important;
}

th.v-data-table-column--fixed {
  background-color: #111111 !important;
}

.v-data-table-column--fixed {
  border-right: none !important;
}

.v-btn--variant-outlined {
  --v-border-opacity: 0.8 !important;
}

.v-data-table__thead {
  .v-data-table__td {
    border-bottom: none !important;
  }
}

.v-data-table .v-data-table-column--last-fixed,
.v-data-table .v-data-table-column--last-fixed:hover {
  border-bottom: none !important;
}

.v-data-table__thead {
  text-transform: none !important;
}

.v-data-table__thead th {
  text-transform: none !important;
}

.v-data-table-header__content {
 text-transform: none !important; 
}

// styles.scss

.v-table {
  &__wrapper {
    tbody {
      tr {
        color: #bbbbbb !important;
        transition: color 0.2s;
      }
      tr:hover,
      tr:has(+ tr.expanded-row:hover),
      tr:hover + tr.expanded-row {
        background-color: #1e1e1e !important;
        color: #ffffff !important;
        .v-data-table-column--fixed {
          background-color: #1e1e1e !important;
          color: #ffffff !important;
        }
      }
    }
  }
}

.w-365 {
  width: 365px !important;
}

.w-300 {
  width: 300px !important;
}

.w-500 {
  width: 500px !important;
}


.v-pagination__list .v-pagination__item .v-btn--icon.v-btn--density-comfortable {
  width: auto;
  min-width: calc(var(--v-btn-height) + 0px);
  padding: 0 4px;
}
.v-autocomplete__content .v-list,
.v-select__content .v-list,
.app-autocomplete__content.v-autocomplete__content {
  background-color: #111111 !important;
}

.v-autocomplete__content .v-list-item--active,
.v-autocomplete__content .v-list-item:hover,
.v-select__content .v-list-item--active,
.v-select__content .v-list-item:hover {
  background-color: #333333 !important;
}

.v-icon {
  font-size: 1.25rem;
  block-size: 1.25rem;
  inline-size: 1.25rem;
}

.v-select .v-icon, .v-autocomplete .v-icon {
  font-size: 1.25rem !important;
  block-size: 1.25rem !important;
  inline-size: 1.25rem !important;
}

.tab-header {
  & > div {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 240px;
    height: 48px;
    padding: 0 24px;
  }
}

