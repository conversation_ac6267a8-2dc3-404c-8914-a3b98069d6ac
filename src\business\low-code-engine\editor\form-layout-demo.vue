<template>
  <div class="form-layout-demo">
    <h1>新增/编辑/详情页面布局演示</h1>
    
    <div class="demo-section">
      <h2>🎯 设计目标</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <h3>📐 响应式宽度</h3>
          <ul>
            <li>1-3个字段：40% 宽度</li>
            <li>4-8个字段：60% 宽度</li>
            <li>9+个字段：80% 宽度</li>
          </ul>
        </div>
        <div class="feature-card">
          <h3>📱 智能布局</h3>
          <ul>
            <li>内容少：独占一行</li>
            <li>内容多：一行两列</li>
            <li>特殊字段：自动全宽</li>
          </ul>
        </div>
        <div class="feature-card">
          <h3>🎨 视觉效果</h3>
          <ul>
            <li>表单区域：#1e1e1e 背景</li>
            <li>居中布局，不铺满全屏</li>
            <li>卡片阴影和圆角</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🏗️ 布局结构</h2>
      <div class="structure-diagram">
        <div class="container-demo">
          <div class="title-card">
            <h4>📋 标题区域</h4>
            <p>包含图标、标题文本</p>
          </div>
          <div class="content-card dark">
            <h4>📝 表单内容区域</h4>
            <p>深色背景 (#1e1e1e)</p>
            <div class="field-layout">
              <div class="field-demo">字段1</div>
              <div class="field-demo">字段2</div>
              <div class="field-demo full-width">全宽字段</div>
            </div>
          </div>
          <div class="actions-card">
            <h4>🔘 操作按钮区域</h4>
            <div class="button-demo">
              <span class="btn-demo primary">SAVE</span>
              <span class="btn-demo outlined">CANCEL</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>📊 宽度适配示例</h2>
      <div class="width-examples">
        <div class="width-example narrow">
          <h4>窄布局 (40%)</h4>
          <p>适用于 1-3 个字段</p>
          <div class="example-form">
            <div class="example-field">用户名</div>
            <div class="example-field">邮箱</div>
            <div class="example-field">密码</div>
          </div>
        </div>
        
        <div class="width-example medium">
          <h4>中等布局 (60%)</h4>
          <p>适用于 4-8 个字段</p>
          <div class="example-form two-cols">
            <div class="example-field">姓名</div>
            <div class="example-field">电话</div>
            <div class="example-field">邮箱</div>
            <div class="example-field">地址</div>
            <div class="example-field">公司</div>
            <div class="example-field">职位</div>
          </div>
        </div>
        
        <div class="width-example wide">
          <h4>宽布局 (80%)</h4>
          <p>适用于 9+ 个字段</p>
          <div class="example-form two-cols">
            <div class="example-field">基本信息1</div>
            <div class="example-field">基本信息2</div>
            <div class="example-field">联系方式1</div>
            <div class="example-field">联系方式2</div>
            <div class="example-field">工作信息1</div>
            <div class="example-field">工作信息2</div>
            <div class="example-field full-width">详细描述</div>
            <div class="example-field">其他信息1</div>
            <div class="example-field">其他信息2</div>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>🎨 样式特性</h2>
      <div class="style-features">
        <div class="style-card">
          <h4>深色表单区域</h4>
          <div class="dark-form-demo">
            <div class="dark-field">
              <label>用户名</label>
              <input type="text" placeholder="请输入用户名" />
            </div>
            <div class="dark-field">
              <label>描述</label>
              <textarea placeholder="请输入描述"></textarea>
            </div>
          </div>
        </div>
        
        <div class="style-card">
          <h4>响应式断点</h4>
          <ul>
            <li>桌面端：按字段数量自适应宽度</li>
            <li>平板端：适当缩小宽度</li>
            <li>手机端：全宽布局</li>
          </ul>
        </div>
        
        <div class="style-card">
          <h4>交互效果</h4>
          <ul>
            <li>淡入动画效果</li>
            <li>按钮悬停状态</li>
            <li>表单验证样式</li>
            <li>加载状态指示</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>💡 实现要点</h2>
      <div class="implementation-points">
        <div class="point-card">
          <h4>1. 容器宽度计算</h4>
          <pre><code>_getFormContainerClass(fieldCount) {
  if (fieldCount <= 3) return "form-container-narrow";
  if (fieldCount <= 8) return "form-container-medium";
  return "form-container-wide";
}</code></pre>
        </div>
        
        <div class="point-card">
          <h4>2. 字段列布局</h4>
          <pre><code>_getFieldColumnProps(field, totalFields) {
  if (isFullWidth) return { cols: 12 };
  if (totalFields <= 3) return { cols: 12 };
  return { cols: 12, md: 6 };
}</code></pre>
        </div>
        
        <div class="point-card">
          <h4>3. 深色主题样式</h4>
          <pre><code>.v-card[style*="background-color: #1e1e1e"] {
  .v-text-field .v-field {
    background-color: #2a2a2a !important;
    border: 1px solid #444 !important;
    color: #ffffff !important;
  }
}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 演示组件，展示新的表单布局设计
</script>

<style scoped>
.form-layout-demo {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
  font-size: 2.5rem;
  font-weight: 300;
}

.demo-section {
  margin-bottom: 50px;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.demo-section h2 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.8rem;
  font-weight: 500;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-card h3 {
  color: #3498db;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.structure-diagram {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}

.container-demo {
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.title-card, .content-card, .actions-card {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.title-card {
  background: white;
  border: 2px solid #3498db;
}

.content-card.dark {
  background: #1e1e1e;
  color: white;
  border: 2px solid #555;
}

.actions-card {
  background: white;
  border: 2px solid #27ae60;
}

.field-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 15px;
}

.field-demo {
  background: #2a2a2a;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  border: 1px solid #444;
}

.field-demo.full-width {
  grid-column: 1 / -1;
}

.width-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.width-example {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.width-example.narrow { border-left: 4px solid #e74c3c; }
.width-example.medium { border-left: 4px solid #f39c12; }
.width-example.wide { border-left: 4px solid #27ae60; }

.example-form {
  margin-top: 15px;
  display: grid;
  gap: 10px;
}

.example-form.two-cols {
  grid-template-columns: 1fr 1fr;
}

.example-field {
  background: #ecf0f1;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.9rem;
  border: 1px solid #bdc3c7;
}

.example-field.full-width {
  grid-column: 1 / -1;
  background: #d5dbdb;
}

.button-demo {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

.btn-demo {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.btn-demo.primary {
  background: #3498db;
  color: white;
}

.btn-demo.outlined {
  background: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.style-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.style-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dark-form-demo {
  background: #1e1e1e;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.dark-field {
  margin-bottom: 15px;
}

.dark-field label {
  display: block;
  color: #cccccc;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.dark-field input,
.dark-field textarea {
  width: 100%;
  background: #2a2a2a;
  border: 1px solid #444;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.dark-field input::placeholder,
.dark-field textarea::placeholder {
  color: #888;
}

.implementation-points {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.point-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.point-card h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.point-card pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.85rem;
  line-height: 1.4;
}

.point-card code {
  font-family: 'Fira Code', 'Courier New', monospace;
}
</style>
