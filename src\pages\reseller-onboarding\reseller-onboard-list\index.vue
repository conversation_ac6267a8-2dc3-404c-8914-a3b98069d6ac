<script setup lang="jsx">
import { ref } from "vue";
import { dateUtil } from "@/utils/day";
import { useTable } from "@/hooks/useTable";
import { PAGINATION_OPTIONS as paginationOptions } from "@/utils/constants.js";
import AppAutocomplete from "@/components/AppAutocomplete.vue";
import message from "@/utils/message.js";
import { confirmDialog } from "@/utils/dialog.js";
import { useRouter } from "vue-router";
import VuePdfEmbed from "vue-pdf-embed";

import {
  VBtn,
  VCard,
  VForm,
  VCardText,
  VCardTitle,
  VCardItem,
  VRow,
  VCol,
} from "vuetify/components";

import { useLookupCodesInfo } from "@/business/reseller-onboarding/reseller-onboarding-list/useLookupCodesInfo.js";
import { useRoleAccessInfo } from "@/business/reseller-onboarding/reseller-onboarding-list/useRoleAccessInfo.js";

import { debounce, isEmpty } from '@/utils/helpers'

import {
  requiredValidator,
  lengthLimitationValidator,
} from "@/utils/validators";

import {
  RESELLER_ONBOARD_MENU_ID,
  SUPER_ADMIN_GROUP_ID,
  RESELLER_ONBOARD_GROUP_ID,
  SUPER_ADMIN_ROLE_ID,
  RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID,
  ADD_ACCESS,
  REJECT_REASONS,
  LOW_TIER_REVIEW_SECTION,
  MEDIUM_TIER_REVIEW_SECTION,
  HIGH_TIER_REVIEW_SECTION,
  REVIEW_REASON,
  OPERATION_TYPE,
  STATUS_MAP,
  showRejectTipStatus,
  PDF_PREVIEW_DIALOG_TYPE,
  PDF,
  IMG,
  TIFF,
  RESELLER_TIER_TYPE,
  enableEnquiryViewStatus,
  enableKYBViewStatus,
  TABLE_HEADER,
} from "@/business/reseller-onboarding/reseller-onboarding-list/constants.js";

import {
  resolveStatusVariant,
  hasSomeElements,
  hasActionPermission,
  hasRole,
  disableCopy,
  disableRightClick,
  disableDevTools,
  parseTiffToImg,
  downloadByBlob,
} from "@/business/reseller-onboarding/reseller-onboarding-list/utils.js";

import {
  enableLinkView,
  enableKYBApprove,
  resellerTierTypeCheck,
  enableEnquiryApprove,
} from "@/business/reseller-onboarding/reseller-onboarding-list/handleViewEnquiryAndKyb.js";

import {
  enableDownloadStatus,
  enableShowDownload,
} from "@/business/reseller-onboarding/reseller-onboarding-list/handleDownload.js";

import {
  enableShowReviewAndApprove,
  enableReviewAndApprove,
} from "@/business/reseller-onboarding/reseller-onboarding-list/handleApprove.js";

// keys are the fields from backend
const headers = ref(TABLE_HEADER);

//*********************************************** define constant variables ***********************************************
const statusList = useLookupCodesInfo("21").lookupCodeList;

const resellerTypeList = useLookupCodesInfo("17").lookupCodeList;

const tpvPerMonthList = useLookupCodesInfo("19").lookupCodeList;

const mainCountryList = useLookupCodesInfo("11").lookupCodeList;

const {roleAccessList} = useRoleAccessInfo(RESELLER_ONBOARD_MENU_ID);

//*********************************************** custom query params ***********************************************
const submissionDate = ref("");

const customGetQueryParams = () => {
  const params = {};
  params.page = searchQuery.value.page;
  params.size = searchQuery.value.size;
  params.resellerEmail = searchQuery.value.resellerEmail;
  params.statusCodes = searchQuery.value.statusCodes;
  params.resellerTypes = searchQuery.value.resellerTypes;
  params.tpvPerMonths = searchQuery.value.tpvPerMonths;
  params.regCountries = searchQuery.value.regCountries;

  // submission date
  if (!isEmpty(submissionDate.value)) {
    const [start, end] = submissionDate.value.split("to");
    if (start) {
      params.submissionDateStart = dateUtil.format(start, "YYYY-MM-DDTHH:mm:ss.SSSZ");
      params.submissionDateEnd = dateUtil.format(start, "YYYY-MM-DDT23:59:59.SSSZ");
    }
    if (end) {
      params.submissionDateEnd = dateUtil.format(end, "YYYY-MM-DDT23:59:59.SSSZ");
    }
  } else {
    params.submissionDateStart = null;
    params.submissionDateEnd = null;
  }

  return params;
};

//*********************************************** use useTable hook to handle table data ***********************************************
const {
  searchQuery,
  page,
  itemsPerPage,
  items: resellerOnboardingList,
  total: totalResellerOnboardingList,
  isLoading,
  handlePageChange,
  handleItemsPerPageChange,
  handleSearch,
  handleSortChange,
  loadData,
} = useTable({
  fetchData() {
    return $api("/api/reseller-onboard/v1/search", {
      method: "POST",
      data: customGetQueryParams(),
      showToast: false
    });
  },
  createDefaultQuery: () => ({
    resellerEmail: null,
    statusCodes: null,
    resellerTypes: null,
    tpvPerMonths: null,
    regCountries: null,
    submissionDateStart: null,
    submissionDateEnd: null,
    page: 1,
    size: 10,
    sortBy: null,
    orderBy: null,
  }),
  rowKey: "resellerOnboardId",
});

//*********************************************** Handle create new reseller ***********************************************
const router = useRouter();

const createNewReseller = () => {
  router.push(
    "/reseller-onboarding/reseller-onboard-list/reseller/add"
  );
};

//*********************************************** Handle view enquiry ***********************************************
const routerEnquiryViewOrPreviewDialog = (item, fileId, previewDialogType, fileName, roleAccessList) => {
  if (enableEnquiryViewStatus.includes(item.statusCode)) {
    routerEnquiryView(item, roleAccessList);
  } else if (enableKYBViewStatus.includes(item.statusCode)) {
    initPreviewDialog(item, fileId, previewDialogType, fileName, roleAccessList);
  } else {
    message.error("Do not have permission to click this link");
  }
};

const routerEnquiryView = (item, roleAccessList) => {
  if (item?.onboardId) {
    const operationBtnFlag = item.statusCode === STATUS_MAP.ENQUIRY_SUBMITTED && enableEnquiryApprove(roleAccessList, item.statusCode);
    localStorage.setItem('reseller-onboard-enquiry-detail', JSON.stringify({ resellerOnboardId: item?.onboardId, operationBtnFlag: operationBtnFlag }))
    router.push(
      '/reseller-onboarding/reseller-onboard-list/reseller/detail'
    );
  } else {
    message.error("Reseller Onboarding ID is not available");
  }
};

//*********************************************** Handle pdf preview ***********************************************
const pdfDialog = ref(false);
const fileDialog = ref(false);
const pdfSource = ref("");
const fileSource = ref("");
const fileNameTemp = ref("");
const isLoadingPdf = ref(false);
const resellerOnboardId = ref(null);
const resellerOnboardVersion = ref(null);
const resellerName = ref(null);
const resellerEmail = ref(null);
const resellerType = ref(null);
const expectedSalesPerMonth = ref(null);
const operationBtnFlag = ref(false);
const tiffPages = ref([]);
const requireKYBFlag = ref(true);

// Initialize the preview dialog
const initPreviewDialog = async (item, fileId, previewDialogType, fileName, roleAccessList) => {

  const approvePermission = item ? enableKYBApprove(roleAccessList, item.statusCode) : false;
  fileNameTemp.value = fileName ? decodeURI(fileName) : "";
  document.addEventListener("keydown", disableDevTools);
  document.addEventListener("copy", disableCopy);
  document.addEventListener("contextmenu", disableRightClick);

  // Open pdf dialog
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    resellerOnboardId.value = item?.onboardId;
    resellerOnboardVersion.value = item?.version;
    requireKYBFlag.value = item?.requireKYBFlag === 1 ? false : true;
    resellerName.value = item?.lastName + " " + item?.firstName;
    resellerEmail.value = item?.email;
    resellerType.value = item?.resellerType;
    expectedSalesPerMonth.value = item?.expectedSalesPerMonth;
    pdfDialog.value = true;
    pdfSource.value = "";
    isLoadingPdf.value = true;
    operationBtnFlag.value = item.statusCode === STATUS_MAP.PENDING_KYB && approvePermission;
  } else {
    // Open file dialog
    fileDialog.value = true;
    fileSource.value = "";
  }

  // Load the file
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    try {
      const pdfUrlApi = approvePermission ? `/api/reseller-onboard/v1/preview` : `/api/reseller-onboard/v1/download`;
      const generatingPdfRes = await $api(
        pdfUrlApi,
        {
          method: "GET",
          params: {
            resellerOnboardId: resellerOnboardId.value,
          },
          showToast: false
        }
      );

      if (generatingPdfRes.data) {
        pdfSource.value = JSON.parse(generatingPdfRes.data).presignedUrl;
      }
    } catch (error) {
      console.error(error);
      message.error("Failed to load preview file");
    }
  } else {
    // Load the document file
    try {
      const res = await $api(`/api/onboard/v1/presigned-url`, {
        method: "GET",
        params: {
          fileId: fileId,
        },
        showToast: false
      });

      if (res.data) {
        if (fileNameTemp.value.includes(TIFF[0])) {
          await parseTiff(JSON.parse(res.data).presignedUrl);
        } else {
          fileSource.value = JSON.parse(res.data).presignedUrl;
        }
      }
    } catch (error) {
      console.error(error);
      message.error("Failed to load document file");
    }
  }
};

const parseTiff = async (url) => {
  tiffPages.value = await parseTiffToImg(url);
}

// Close the preview dialog
const closeDialog = (previewDialogType) => {
  if (PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG === previewDialogType) {
    pdfDialog.value = false;
    closeFileDialog();
  } else {
    closeFileDialog();
  }

  document.removeEventListener("copy", disableCopy);
  document.removeEventListener("contextmenu", disableRightClick);
};


const closeFileDialog = () => {
  fileDialog.value = false;
};

// Handle the pdf rendered event
const handlePdfRendered = (roleAccessList) => {
  isLoadingPdf.value = false;
  const links = document.querySelectorAll(".pdf-viewer a");
  links.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();

      const urlArray = e.target.href.split('/');
      const fileName = urlArray.length >= 1 ? urlArray[urlArray.length - 1] : null;
      const fileId = urlArray.length >= 2 ? urlArray[urlArray.length - 2] : null;

      initPreviewDialog(
        null,
        fileId,
        PDF_PREVIEW_DIALOG_TYPE.FILE_DIALOG,
        fileName,
        roleAccessList
      );
    });
  });
};

// Handle the error event
const handlePreviewError = (e) => {
  isLoadingPdf.value = true;
  console.error(e);
  message.error("Failed to load preview file");
};

// Handle refersh pdf
const handlePdfRefresh = async () => {
  const temp = pdfSource.value;
  pdfSource.value = null;

  await nextTick();
  isLoadingPdf.value = true;
  pdfSource.value = temp;
};

const refreshPdf = debounce(handlePdfRefresh, 1000)

window.addEventListener('resize', refreshPdf)

//*********************************************** confirm approval handle function ***********************************************

const selectedReason = ref(null);
const remarks = ref(null);
const reviewRows = ref([
  { reviewSection: null, reviewReason: null, remarks: null },
]);
const isInProgress = ref(false);
const isInValid = ref(false);
const reviewSectionList = ref([]);

const handleOperationClick = (type) => {
  selectedReason.value = null;
  remarks.value = null;
  reviewRows.value = [
    { reviewSection: null, reviewReason: null, remarks: null },
  ];
  if (type === OPERATION_TYPE.REVIEW) {
    const tierType = resellerTierTypeCheck(resellerType.value, expectedSalesPerMonth.value);
    if (tierType === RESELLER_TIER_TYPE.LOW) {
      reviewSectionList.value = LOW_TIER_REVIEW_SECTION;
    } else if (tierType === RESELLER_TIER_TYPE.MEDIUM) {
      reviewSectionList.value = MEDIUM_TIER_REVIEW_SECTION;
    } else {
      reviewSectionList.value = HIGH_TIER_REVIEW_SECTION;
    }
  }

  const textContent =
    type === OPERATION_TYPE.APPROVE
      ? <div>Once approved, the reseller will be ready to be onboarded.</div>
      : type === OPERATION_TYPE.REJECT
        ? <div>Once rejected, the reseller account will be auto disabled, and the entire reseller onboarding process will be terminated. <br />This action cannot be undone.</div>
        : type === OPERATION_TYPE.REVIEW
          ? <div>Once your review is submitted, the reseller will revise the content based on your feedback and resubmit it for approval. <br />You must provide <B>at least one row of feedback</B> details to submit your review. Feel free to add more details by clicking 'Add'. Please note, this action cannot be undone.</div>
          : <div></div>;

  const toastMsg =
    type === OPERATION_TYPE.APPROVE
      ? "Reseller Onboarding KYB is successfully approved"
      : type === OPERATION_TYPE.REJECT
        ? "Reseller Onboarding KYB is successfully rejected"
        : type === OPERATION_TYPE.REVIEW
          ? "Reseller Onboarding KYB is successfully sent for review"
          : "";

  const width =
    type === OPERATION_TYPE.APPROVE
      ? 400
      : type === OPERATION_TYPE.REJECT
        ? 600
        : type === OPERATION_TYPE.REVIEW
          ? 1000
          : 400;

  confirmDialog({
    title: `${type === OPERATION_TYPE.APPROVE
      ? "Approve Reseller Onboarding KYB?"
      : type === OPERATION_TYPE.REJECT
        ? "Reject Reseller Onboarding KYB?"
        : type === OPERATION_TYPE.REVIEW
          ? "Review Reseller Onboarding KYB?"
          : ""
      }`,
    text: textContent,
    width,
    render: () => {
      return (
        <div>
          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.APPROVE ? "block" : "none",
            }}
          >
            <AppTextField
              v-model={remarks.value}
              label="Remarks (Optional)"
              placeholder="Type a reason"
              rules={[(value) => lengthLimitationValidator(value, 250, "Remarks")]}
            />
          </div>
          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.REJECT ? "block" : "none",
            }}
          >
            <AppSelect
              items={REJECT_REASONS}
              label="Reject Reason*"
              placeholder="Select a reason"
              v-model={selectedReason.value}
              item-text="text"
              item-value="value"
              rules={[requiredValidator]}
            />
            <AppTextField
              v-model={remarks.value}
              label="Remarks (Optional)"
              placeholder="Type a reason"
              rules={[(value) => lengthLimitationValidator(value, 250, "Remarks")]}
            />
          </div>

          <div
            class="px-6 mb-10"
            style={{
              display: type === OPERATION_TYPE.REVIEW ? "block" : "none",
            }}
          >
            <div style="max-height: 30vh;overflow-y: auto;padding: 15px; ">

              {reviewRows.value.map((row, index) => (

                <div key={index} style="margin-bottom:5px; ">
                  <VCard>
                    <VCardText style="background-color: #2D2D2D;">
                      <VForm>
                        <VRow key={index} class="d-flex gap-2" style="font-size:18px; position: relative;">
                          Feedback {index + 1}
                          <div onClick={() => handleDelReviewRowClick(index)} style="position: absolute;right: 0;"
                            style={{
                              display: index !== 0 ? "block" : "none",
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="review-trash-can">
                              <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z" />
                            </svg>
                          </div>

                        </VRow>
                        <VRow key={index} class="d-flex gap-2">
                          <VCol md="5" sm="6" cols="12">
                            <AppSelect
                              items={reviewSectionList.value}
                              label="Review Section*"
                              placeholder="Select Review Section"
                              v-model={row.reviewSection}
                              item-text="text"
                              item-value="value"
                              rules={[requiredValidator]}
                            />
                          </VCol>
                          <VCol md="5" sm="6" cols="12">
                            <AppSelect
                              items={REVIEW_REASON}
                              label="Review Reason*"
                              placeholder="Select Review Reason"
                              v-model={row.reviewReason}
                              item-text="text"
                              item-value="value"
                              rules={[requiredValidator]}
                            />
                          </VCol>
                        </VRow>
                        <VRow key={index} class="d-flex gap-2">
                          <VCol md="10" sm="6" cols="12">
                            <AppTextField
                              v-model={row.remarks}
                              label="Remarks (Optional)"
                              placeholder="Type a reason"
                              rules={[(value) => lengthLimitationValidator(value, 500, "Remarks")]}
                            />
                          </VCol>
                        </VRow>
                      </VForm>
                    </VCardText>
                  </VCard>
                </div>
              ))}
            </div>
            <div
              class="d-flex align-center flex-wrap gap-4"
              style="justify-content: center;align-items: center;margin-top:20px;"
            >
              <VBtn
                variant="outlined"
                color="#44D62C"
                style="border:2px solid #30961f;"
                disabled={reviewRows.value.length === (reviewSectionList.value.length * 10)}
                onClick={() => handleAddReviewRow()}
              >
                ADD
              </VBtn>
            </div>
          </div>
        </div>
      );
    },
    confirmButtonText: `${type === OPERATION_TYPE.APPROVE
      ? "APPROVE"
      : type === OPERATION_TYPE.REJECT
        ? "REJECT"
        : type === OPERATION_TYPE.REVIEW
          ? "REVIEW"
          : ""
      }`,
    confirmButtonColor: `${type === OPERATION_TYPE.APPROVE
      ? "#44D62C"
      : type === OPERATION_TYPE.REJECT
        ? "#FD4949"
        : type === OPERATION_TYPE.REVIEW
          ? "#FD8611"
          : ""
      }`,
    confirmButtonTextColor: "#000000",
    cancelButtonText: "CANCEL",
    onCancel: () => {
      isInProgress.value = false;
      isInValid.value = false
    },
    async onConfirm(close) {
      if (isInProgress.value) return;
      isInProgress.value = true;
      isInValid.value = false;

      if (type === OPERATION_TYPE.APPROVE) {
        if (remarks.value && remarks.value.length > 250) {
          isInProgress.value = false;
          message.error(
            "The Remark field length must be less than 250 characters"
          );
          return;
        }
      }

      if (type === OPERATION_TYPE.REJECT) {
        if (isEmpty(selectedReason.value)) {
          isInProgress.value = false;
          message.error("Must select Reject Reason");
          return;
        }

        if (remarks.value && remarks.value.length > 250) {
          isInProgress.value = false;
          message.error(
            "The Remark field length must be less than 250 characters"
          );
          return;
        }
      }

      const reviewRowsParams = [];
      if (type === OPERATION_TYPE.REVIEW) {
        for (let i = 0; i < reviewRows.value.length; i++) {
          const row = reviewRows.value[i];
          if (isEmpty(row.reviewSection)) {
            isInValid.value = true;
            message.error("Must select Review Section");
            break;
          }

          if (isEmpty(row.reviewReason)) {
            isInValid.value = true;
            message.error("Must select Review Reason");
            break;
          }

          if (row.remarks && row.remarks.length > 500) {
            isInValid.value = true;
            message.error("The remarks field length must be less than 500 characters");
            break
          }

          reviewRowsParams.push(
            {
              reviewSectionCode: LOW_TIER_REVIEW_SECTION.indexOf(row.reviewSection) + 1,
              reviewSection: row.reviewSection,
              reviewReasonCode: REVIEW_REASON.indexOf(row.reviewReason) + 1,
              reviewReason: row.reviewReason,
              remarks: row.remarks,
            }
          );
        }
      }

      if (isInValid.value) {
        isInProgress.value = false;
        return;
      }
      try {
        await $api("/api/reseller-onboard/v1/status", {
          method: "POST",
          data: {
            onboardId: resellerOnboardId.value,
            email: resellerEmail.value,
            resellerName: resellerName.value,
            statusCode: type,
            reason:
              type === OPERATION_TYPE.REJECT
                ? JSON.stringify([{
                  reviewSectionCode: 0,
                  reviewReasonCode: REJECT_REASONS.indexOf(selectedReason.value) + 1,
                }])
                : type === OPERATION_TYPE.REVIEW
                  ? JSON.stringify(reviewRowsParams)
                  : null,
            remark: (type === OPERATION_TYPE.REJECT || type === OPERATION_TYPE.APPROVE) ? remarks.value : null,
            description:
              type === OPERATION_TYPE.REJECT
                ? "kybReject"
                : type === OPERATION_TYPE.REVIEW
                  ? "kybReview"
                  : "kybApprove",
            version: resellerOnboardVersion.value,
          },
          showToast: false
        });
        message.success(toastMsg);
        close();
        closeDialog(PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG);
        loadData();
      } catch (error) {
        console.error(error);
        message.error("Failed to update status");
      } finally {
        isInProgress.value = false;
      }
    },
  });
};

const handleDelReviewRowClick = (rowIndex) => {
  confirmDialog({
    title: `Remove Feedback ${rowIndex + 1}?`,
    text: 'Once removed, this action cannot be undone.',
    confirmButtonText: 'REMOVE',
    confirmButtonColor: 'primary',
    confirmButtonTextColor: '#000000',
    cancelButtonText: 'CANCEL',
    async onConfirm(close) {
      close()
      handleDelReviewRow(rowIndex)
    }
  })
};

const handleAddReviewRow = function () {
  reviewRows.value.push({
    reviewSection: null,
    reviewReason: null,
    remarks: null,
  });
};

const handleDelReviewRow = function (rowIndex) {
  reviewRows.value.splice(rowIndex, 1);
};

//*********************************************** download handle function ***********************************************
const handleDownloadFile = async (item) => {
  try {
    const generatingPdfRes = await $api(
      `/api/reseller-onboard/v1/download`,
      {
        method: "GET",
        params: {
          resellerOnboardId: item.onboardId,
        },
        showToast: false
      }
    );

    if (generatingPdfRes.data) {
      await downloadByBlob(JSON.parse(generatingPdfRes.data).presignedUrl, "resellerOnobardPreview.pdf");

      message.success("Download in progress, and will begin shortly");
    }
  } catch (error) {
    console.error(error);
    message.error("Failed to download file");
  }
};

</script>

<template>
  <section>
    <VCard class="mb-6">
      <VCardItem class="pb-4 px-0">
        <VCardTitle>
          <div class="d-flex align-center flex-wrap">
            Reseller Onboarding List
            <VSpacer />
            <div class="d-flex align-center gap-4">
              <VBtn v-if="hasActionPermission(roleAccessList, RESELLER_ONBOARD_GROUP_ID, RESELLER_ONBOARD_RESELLER_OPS_ENQUIRY_ROLE_ID, ADD_ACCESS)
                || hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, ADD_ACCESS)"
                @click.stop="createNewReseller">
                CREATE
              </VBtn>
            </div>
          </div>
        </VCardTitle>
      </VCardItem>

      <VCardText class="px-0">
        <VRow>
          <!-- 👉 Search Email -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppTextField v-model="searchQuery.resellerEmail" placeholder="Search Email" outline-label="Email" clearable
              @update:model-value="handleSearch" prepend-inner-icon="mdi-magnify" />
          </VCol>

          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.statusCodes" placeholder="Select Status" :items="statusList" clearable
              outline-label="Status" clear-icon="tabler-x" multiple filterable chips closable-chips
              @update:model-value="handleSearch" />
          </VCol>

          <!-- 👉 Select Reseller Type -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.resellerTypes" placeholder="Select Reseller Type"
              :items="resellerTypeList" clearable outline-label="Reseller Type" clear-icon="tabler-x" multiple
              filterable chips closable-chips @update:model-value="handleSearch" />
          </VCol>

          <!-- 👉 Select TPV Per Month -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.tpvPerMonths" placeholder="Select TPV Per Month"
              :items="tpvPerMonthList" clearable outline-label="TPV Per Month" clear-icon="tabler-x" multiple filterable
              chips closable-chips @update:model-value="handleSearch" />
          </VCol>

          <!-- 👉 Select Main Country -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppAutocomplete v-model="searchQuery.regCountries" placeholder="Select Country"
              :items="mainCountryList" clearable outline-label="Country" clear-icon="tabler-x" multiple filterable
              chips closable-chips @update:model-value="handleSearch" />
          </VCol>

          <!-- 👉 Search Submission Date -->
          <VCol cols="12" sm="4" xl="3" xxl="2">
            <AppDateTimePicker v-model="submissionDate" :config="{
              mode: 'range',
              enableTime: false,
              dateFormat: 'Y-m-d',
            }" placeholder="Search Submission Date" clearable @update:model-value="handleSearch"
              outline-label="Submission Date" />
          </VCol>
        </VRow>
      </VCardText>

      <VDivider />

      <!-- SECTION datatable -->
      <VDataTableServer :items="resellerOnboardingList" item-value="resellerOnboardId"
        :items-length="totalResellerOnboardingList" :headers="headers" :loading="isLoading" class="text-no-wrap">
        <!-- Reseller ID -->
        <template #[`item.systemAccountId`]="{ item }">
          {{ isEmpty(item.systemAccountId) ? '-' : item.systemAccountId }}
        </template>

        <!-- Reseller Name -->
        <template #[`item.lastName`]="{ item }">
          <div v-if="enableLinkView(roleAccessList, item.statusCode)" :title="`${item.lastName} ${item.firstName}`"
            class="truncate-content" style="width: 200px;">
            <AppText
              @click.prevent="routerEnquiryViewOrPreviewDialog(item, null, PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG, null, roleAccessList)"
              :auth="['View', 'ResellerOnboardingList']">
              {{ item.lastName }} {{ item.firstName }}
            </AppText>
          </div>
          <div v-else :title="`${item.lastName} ${item.firstName}`" class="truncate-content" style="width: 200px;">
            {{ item.lastName }} {{ item.firstName }}
          </div>
        </template>

        <!-- Email -->
        <template #[`item.email`]="{ item }">
          {{ item.email }}
        </template>

        <!-- Contact Number -->
        <template #[`item.phoneCountryCode`]="{ item }">
          {{ isEmpty(item.phoneCountryCode) ? '-' : `+${item.phoneCountryCode} ${item.phoneNo}` }}
        </template>

        <!-- Reseller Type -->
        <template #[`item.resellerTypeContent`]="{ item }">
          {{ isEmpty(item.resellerTypeContent) ? '-' : item.resellerTypeContent }}
        </template>

        <!-- Company Name -->
        <template #[`item.regName`]="{ item }">
          <div :title="item.regName" class="truncate-content" style="width: 260px;">
            {{ isEmpty(item.regName) ? '-' : item.regName }}
          </div>
        </template>

        <!-- Registered Country -->
        <template #[`item.regCountryContent`]="{ item }">
          {{ isEmpty(item.regCountryContent) ? '-' : item.regCountryContent }}
        </template>

        <!-- Main Distribution Country -->
        <template #[`item.mainDistributionCountryContent`]="{ item }">
          {{ isEmpty(item.mainDistributionCountryContent) ? '-' : item.mainDistributionCountryContent }}
        </template>

        <!-- Submitted On -->
        <template #[`item.submittedOn`]="{ item }">
          {{ isEmpty(item.submittedOn) ? '-' : dateUtil.format(item.submittedOn) }}
        </template>

        <!-- TPV Per Month -->
        <template #[`item.expectedSalesPerMonthContent`]="{ item }">
          {{ isEmpty(item.expectedSalesPerMonthContent) ? '-' : item.expectedSalesPerMonthContent }}
        </template>

        <!-- Status -->
        <template #[`item.statusCode`]="{ item }">
          <span class="d-flex align-center">
            <VIcon :icon="resolveStatusVariant(item.statusCode, 'icon')"
              :color="resolveStatusVariant(item.statusCode, 'color')"
              :size="resolveStatusVariant(item.statusCode, 'size')"
              :style="resolveStatusVariant(item.statusCode, 'iconStyle') || {}" />
            <span class="ms-1">{{ item.statusContent }}</span>
            <VTooltip v-if="showRejectTipStatus.includes(item.statusCode) && item.rejectReason" location="top">
              <template #activator="{ props }">
                <VIcon v-bind="props" icon="mdi-help-circle-outline" size="15" class="ms-1" />
              </template>
              <span>
                {{ item.rejectReason }}
                <span v-if="item.remark">: {{ item.remark }}</span>
              </span>
            </VTooltip>
          </span>
        </template>

        <!-- Last Modified -->
        <template #[`item.lastModified`]="{ item }">
          {{ isEmpty(item.lastModified) ? '-' : dateUtil.format(item.lastModified) }}
        </template>

        <!-- Actions -->
        <template #[`item.actions`]="{ item }">
          <div class="actions-grid">
            <div class="action-item">
              <IconBtn v-if="enableShowReviewAndApprove(roleAccessList)"
                @click.stop="routerEnquiryViewOrPreviewDialog(item, null, PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG, null, roleAccessList)"
                :disabled="!enableReviewAndApprove(roleAccessList, item.statusCode)" v-tooltip="'Review & Approve'">
                <VIcon icon="mdi-file-document-edit"
                  :class="enableReviewAndApprove(roleAccessList, item.statusCode) ? 'hover:text-primary' : 'text-disable'" />
              </IconBtn>
              <IconBtn v-if="enableShowDownload(roleAccessList)" @click.stop="handleDownloadFile(item)"
                :disabled="!enableDownloadStatus.includes(item.statusCode)" v-tooltip="'Download'">
                <VIcon icon="mdi-download"
                  :class="enableDownloadStatus.includes(item.statusCode) ? 'hover:text-primary' : 'text-disable'" />
              </IconBtn>
            </div>
          </div>
        </template>

        <!-- pagination -->
        <template #bottom>
          <TablePagination :page="page" :items-per-page="itemsPerPage" :total-items="totalResellerOnboardingList"
            @update:page="handlePageChange">
            <div class="d-flex gap-3">
              <AppSelect :model-value="itemsPerPage" :items="paginationOptions"
                @update:model-value="handleItemsPerPageChange" />
            </div>
          </TablePagination>
        </template>
      </VDataTableServer>
    </VCard>

    <div v-show="pdfDialog" class="dialog-mask">
      <div class="panel-a">
        <div class="pdf-box">
          <v-card>
            <div class="pdf-drag-handle">
              <v-card-title class="d-flex justify-space-between align-center">
                <span>Preview Document</span>
                <v-icon @click.stop="closeDialog(PDF_PREVIEW_DIALOG_TYPE.PDF_DIALOG)">mdi-close-circle</v-icon>
              </v-card-title>
            </div>
            <div class="pdf-content">
              <v-card-text class="pa-0">
                <div class="pdf-viewer no-print">
                  <div v-if="isLoadingPdf">Loading...</div>
                  <vue-pdf-embed v-if="pdfDialog" :source="pdfSource" @rendered="handlePdfRendered(roleAccessList)"
                    text-layer annotationLayer @loading-failed="handlePreviewError"
                    @rendering-failed="handlePreviewError" />
                </div>
              </v-card-text>
            </div>
            <div :class="[operationBtnFlag ? 'pdf-footer' : 'pdf-footer-plus']">
              <VRow class="d-flex justify-end gap-2" v-if="operationBtnFlag">
                <VBtn :disabled="isLoadingPdf" variant="outlined" color="default"
                  @click.stop="handleOperationClick(OPERATION_TYPE.REJECT)">
                  REJECT
                </VBtn>
                <VBtn v-if="requireKYBFlag" :disabled="isLoadingPdf" color="warning" class="review-btn"
                  @click.stop="handleOperationClick(OPERATION_TYPE.REVIEW)">
                  REVIEW
                </VBtn>
                <VBtn :disabled="isLoadingPdf" @click.stop="handleOperationClick(OPERATION_TYPE.APPROVE)">
                  APPROVE
                </VBtn>
              </VRow>
            </div>
          </v-card>
        </div>
      </div>
      <div class="panel-b" v-show="fileDialog">
        <div class="file-box" v-show="fileDialog">
          <v-card>
            <div class="file-drag-handle">
              <v-card-title class="d-flex justify-space-between align-center">
                <span :title="fileNameTemp" class="file-title">
                  {{ fileNameTemp }}
                </span>
                <v-icon @click.stop="closeDialog(PDF_PREVIEW_DIALOG_TYPE.FILE_DIALOG)">mdi-chevron-left</v-icon>
              </v-card-title>
            </div>
            <div class="pdf-content">
              <div v-if="hasSomeElements(fileNameTemp, PDF) && fileSource" class="file-viewer no-print">
                <vue-pdf-embed :source="fileSource" @loading-failed="handlePreviewError"
                  @rendering-failed="handlePreviewError" />
              </div>
              <div v-if="hasSomeElements(fileNameTemp, IMG) && fileSource" class="file-viewer no-print">
                <img :src="fileSource" @error="handlePreviewError" class="img-file-viewer" />
              </div>
              <div v-if="hasSomeElements(fileNameTemp, TIFF) && tiffPages && tiffPages.length > 0"
                class="file-viewer no-print">
                <div v-for="(page, idx) in tiffPages" :key="idx">
                  <img :src="page" @error="handlePreviewError" class="img-file-viewer" />
                </div>
              </div>
            </div>
          </v-card>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./css/index.scss">
</style>

<style lang="scss" src="./css/dialog.scss">
</style>
