<template>
  <LowCodeLayout navigation-value="Scenario">
    <div class="low-code-home">
      <!-- Page title -->
      <div class="page-header">
        <h1>Scenario</h1>
        <p>Visual page design platform based on metadata-driven architecture</p>
      </div>

          <!-- Scenario grid -->
    <VRow>
      <VCol
        v-for="scenario in scenarios"
        :key="scenario.scenarioId"
        cols="12"
        md="6"
        lg="3"
      >
        <VCard
          class="scenario-card h-[178px]"
          hover
          @click="openScenarioEditor(scenario.scenario)"
        >
          <VCardText class="pa-6">
            <div class="d-flex align-start mb-4">
              <VAvatar
                :color="scenario.color"
                size="48"
                class="mr-4"
              >
                <VIcon :icon="scenario.icon" />
              </VAvatar>
              
              <div class="flex-grow-1">
                <h3 class="text-h6 font-weight-bold mb-1">{{ scenario.scenario }}</h3>
                <p class="line-2 text-grey-darken-1 mb-0">{{ scenario.description }}</p>
              </div>
              
              <!-- <VMenu>
                <template #activator="{ props }">
                  <VBtn
                    icon
                    variant="text"
                    size="small"
                    v-bind="props"
                    @click.stop
                  >
                    <VIcon>mdi-dots-vertical</VIcon>
                  </VBtn>
                </template>
                
                <VList class="scenario-menu" density="compact">
                  <VListItem
                    @click="handleMenuAction('toggle-status', scenario)"
                    class="scenario-menu-item"
                  >
                    <template #prepend>
                      <VIcon 
                        :icon="scenario.status === 'Active' ? 'mdi-pause-circle-outline' : 'mdi-play-circle-outline'"
                        :color="scenario.status === 'Active' ? 'warning' : 'success'"
                        size="20"
                      />
                    </template>
                    <VListItemTitle>
                      {{ scenario.status === 'Active' ? 'Disable' : 'Enable' }}
                    </VListItemTitle>
                  </VListItem>
                  
              
                </VList>
              </VMenu> -->
            </div>
            
            <div class="d-flex gap-4 text-caption text-grey">
              <span>
                <VIcon size="small" class="mr-1">mdi-format-list-bulleted</VIcon>
                {{ scenario.fields.length || 0 }} fields
              </span>
              <span>
                <VIcon size="small" class="mr-1">mdi-file-document</VIcon>
                {{ scenario.pages }} pages
              </span>
              <span>
                <VIcon 
                  size="small" 
                  class="mr-1"
                  :color="scenario.status === 'Active' ? 'success' : 'warning'"
                >
                  mdi-circle
                </VIcon>
                {{ scenario.status }}
              </span>
            </div>
          </VCardText>
        </VCard>
      </VCol>
      
      <!-- Create new scenario card -->
      <VCol cols="12" md="6" lg="3">
        <VCard
          class="h-100 create-card"
          variant="outlined"
          @click="handleCreateScenario"
        >
          <VCardText class="d-flex flex-column align-center justify-center pa-6 text-center">
            <VAvatar
              size="48"
              variant="outlined"
              class="mb-4"
            >
              <VIcon>mdi-plus</VIcon>
            </VAvatar>
            <h3 class="text-h6 font-weight-bold mb-2">Create New Scenario</h3>
            <p class="text-body-2 text-grey-darken-1">Start building a new business scenario</p>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <CreateScenarioDialog
      v-model="isCreateScenarioDialogVisible"
      @submit="handleScenarioSubmit"
    />


    </div>
  </LowCodeLayout>
</template>

<script setup>
import { ref, computed, onMounted, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import LowCodeLayout from '@/business/low-code-engine/layout/LowCodeLayout.vue'
import CreateScenarioDialog from '@/business/low-code-engine/scenario/CreateScenarioDialog.vue'
import { useScenarioConfig } from '@/business/low-code-engine/hooks/useScenarioConfig'

definePage({
  alias: '/low-code-engine',
  name: 'low-code-engine',
  meta: {
    layout: 'blank',
    public: true,
  },
})

// ===== State =====
const router = useRouter()

// Scenario configuration management
const {
  scenarioConfigs,
  getScenarioConfig,
  saveScenarioConfig,
  updateScenarioConfig,
} = useScenarioConfig()

// Base scenario data (from backend)
const baseScenarios = ref([])

watchEffect(() => {
  // Use watchEffect to reactively update baseScenarios when scenarioConfigs changes.
  // This handles the initial asynchronous loading correctly.
  if (scenarioConfigs.value) {
    baseScenarios.value = Array.from(scenarioConfigs.value.values());
  }
});


// Computed property: merge base data with local configs
const scenarios = computed(() => {
  return baseScenarios.value.map(scenario => {
    // The scenario object from baseScenarios is already the full config
    return {
      ...scenario,
      icon: scenario.icon || 'mdi-puzzle-outline',
      color: scenario.color || 'primary'
    }
  })
})

const isCreateScenarioDialogVisible = ref(false)


// ===== Methods =====

/**
 * Open scenario editor
 */
function openScenarioEditor(scenario) {
  
  router.push(`/low-code-engine/editor?scenario=${scenario}`)
}

/**
 * Create new scenario
 */
function handleCreateScenario() {
  isCreateScenarioDialogVisible.value = true
}

/**
 * Handle new scenario submission
 */
async function handleScenarioSubmit(formData) {
  try {
    // Automatically generate scenario ID (simulated backend generation)
    const generateScenarioId = (scenarioName) => {
      const timestamp = Date.now()
      const nameSlug = scenarioName.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Multiple hyphens combined into one
        .replace(/^-|-$/g, '') // Remove leading and trailing hyphens
      
      return `scenario-${nameSlug}-${timestamp}`
    }

    const scenarioId = generateScenarioId(formData.scenario)
    
    // Prepare the full scenario object to be saved
    const scenarioConfig = {
      scenarioId,
      scenario: formData.scenario,
      description: formData.description,
      icon: formData.icon,
      color: formData.color,
    }
    
    // Save configuration to IndexedDB.
    // The watchEffect will automatically update the UI.
    await saveScenarioConfig(scenarioConfig)

    
    // Here you can add API call logic to save to backend
    
  } catch (error) {
    console.error('Failed to create scenario:', error)
    // You can add error handling UI here
  }
}




/**
 * Handle menu item click
 */
function handleMenuAction(action, scenario) {
  switch (action) {
    case 'toggle-status':
      toggleScenarioStatus(scenario)
      break
    case 'edit':
      editScenario(scenario)
      break
  }
  
}

/**
 * Toggle scenario status
 */
async function toggleScenarioStatus(scenario) {
  try {
    const newStatus = scenario.status === 'Active' ? 'Inactive' : 'Active'
    
    // Update the status in IndexedDB
    // The UI will update reactively via watchEffect
    await updateScenarioConfig(scenario.scenarioId, {
      status: newStatus
    })
    
    console.log(`Scenario ${scenario.scenarioId} status updated to ${newStatus}`)
    
  } catch (error) {
    console.error('Failed to toggle scenario status:', error)
  }
}

/**
 * Edit scenario
 */
function editScenario(scenario) {
  router.push(`/low-code-engine/editor?scenario=${scenario.scenario}`)
}

/**
 * Initialize default configurations for existing scenarios
 */
async function initializeDefaultConfigs() {
  try {
    const defaultConfigs = [
      {
        scenarioId: 'demo-user-management',
        name: 'User Management',
        description: 'User management system with authentication and role-based access control',
        icon: 'mdi-account-group',
        color: 'success',
        fields: 12,
        pages: 3,
        status: 'Active'
      },
      {
        scenarioId: 'demo-product-catalog', 
        name: 'Product Catalog',
        description: 'Complete product management with categories, inventory tracking and pricing',
        icon: 'mdi-shopping',
        color: 'primary',
        fields: 35,
        pages: 5,
        status: 'Active'
      },
      {
        scenarioId: 'demo-order-system',
        name: 'Order Management', 
        description: 'End-to-end order processing with payment integration and fulfillment tracking',
        icon: 'mdi-cart',
        color: 'warning',
        fields: 20,
        pages: 4,
        status: 'Inactive'
      }
    ]

    // Check and save default configs for scenarios that don't have configs yet
    for (const config of defaultConfigs) {
      const existingConfig = getScenarioConfig(config.scenarioId)
      if (!existingConfig || existingConfig.scenarioId === config.scenarioId && !existingConfig.createdAt) {
        await saveScenarioConfig(config.scenarioId, config)
        console.log(`Default config saved for ${config.scenarioId}`)
      }
    }
  } catch (error) {
    console.error('Failed to initialize default configs:', error)
  }
}

// Initialize on component mount
onMounted(() => {
  // Wait a bit for the IndexedDB to be ready
  
})


</script>

<style scoped>
.low-code-home {
  padding: 24px;
  background: #0f0f0f;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 48px;
}

.page-header h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.page-header p {
  color: #888;
  font-size: 1.1rem;
}

.scenario-card {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 12px !important;
}

.scenario-card:hover {
  transform: translateY(-4px);
  border-color: #4caf50 !important;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3) !important;
}

.scenario-card .v-card-text {
  color: white;
}

.create-card {
  background: transparent !important;
  border: 2px dashed #333 !important;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 12px !important;
}

.create-card:hover {
  border-color: #4caf50 !important;
  background: rgba(76, 175, 80, 0.05) !important;
}

.create-card .v-card-text {
  color: #888;
}

.create-card:hover .v-card-text {
  color: #4caf50;
}

/* Feature entry card */
:deep(.v-card) {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  color: white !important;
  border-radius: 12px !important;
}

:deep(.v-card:hover) {
  border-color: #4caf50 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2) !important;
}

:deep(.v-card-title) {
  color: white !important;
  background: #1a1a1a !important;
}

:deep(.v-card-text) {
  color: white !important;
}

:deep(.text-grey-darken-1) {
  color: #888 !important;
}

:deep(.v-btn) {
  text-transform: none;
  font-weight: 500;
}

:deep(.v-chip) {
  border-radius: 6px;
}

/* Scenario menu styles */
.scenario-menu {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  border-radius: 8px !important;
  min-width: 160px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4) !important;
}

.scenario-menu-item {
  color: white !important;
  border-radius: 4px !important;
  margin: 2px 4px !important;
  transition: all 0.2s ease !important;
}

.scenario-menu-item:hover {
  background: rgba(76, 175, 80, 0.1) !important;
  color: #4caf50 !important;
}

.scenario-menu-item:hover .v-icon {
  color: #4caf50 !important;
}

:deep(.scenario-menu .v-list-item-title) {
  color: white !important;
  font-size: 14px !important;
}


</style>
