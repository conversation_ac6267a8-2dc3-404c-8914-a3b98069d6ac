import {
  STATUS_MAP,
  SUPER_ADMIN_GROUP_ID,
  <PERSON><PERSON><PERSON><PERSON><PERSON>_ACCESS,
  SUPER_ADMIN_ROLE_ID,
  RESELLER_ONBOARD_GROUP_ID,
  RESELLER_ONBOARD_RESELLER_OPS_KYB_ROLE_ID,

} from "@/business/reseller-onboarding/reseller-onboarding-list/constants.js";

import {
  hasActionPermission,
} from "@/business/reseller-onboarding/reseller-onboarding-list/utils.js";

export const enableDownloadStatus = [STATUS_MAP.PENDING_KYB, STATUS_MAP.KYB_SUCCESS, STATUS_MAP.PROCESSING_KYB, STATUS_MAP.KYB_FAILED];

export const enableShowDownload = (roleAccessList) => {
  return hasActionPermission(roleAccessList, SUPER_ADMIN_GROUP_ID, SUPER_ADMIN_ROLE_ID, DOWNLOAD_ACCESS) ||
    hasActionPermission(roleAccessList, RES<PERSON>LER_ONBOARD_GROUP_ID, RES<PERSON>LER_ONBOARD_RESELLER_OPS_KYB_ROLE_ID, DOWNLOAD_ACCESS);
};