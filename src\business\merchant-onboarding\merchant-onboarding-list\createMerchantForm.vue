<script setup>
import {
  requiredValidator,
  merchanEmailValidator,
  merchantNameValidator,
} from "@/utils/validators";

// form data
const formModel = defineModel({
  type: Object,
  required: true,
});

const props = defineProps({
  type: {
    type: String,
    required: true,
  },
});

const formRef = ref(null);

// expose methods and data to parent component
defineExpose({
  form: formRef,
  validate: () => {
    return formRef.value.validate();
  },
  getParams: () => {
    const params = {
      name: formModel.value.userName.trim(),
      email: formModel.value.userEmail.trim(),
    };
    return params;
  },
  resetForm: () => {
    formModel.value = {
      userName: null,
      userEmail: null,
    };
  },
});
</script>

<template>
  <VCard ref="cardRef">
    <VCardItem class="pb-4 px-0">
      <VCardTitle> Create New Merchant Account </VCardTitle>
    </VCardItem>
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5" style="width: 40%">
        <div class="form-wrapper">
          <VCardTitle> 1. Enter Merchant Account Info </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <AppTextField
                    v-model="formModel.userName"
                    label="Merchant Name*"
                    placeholder="Type merchant name"
                    :rules="[
                      requiredValidator,
                      merchantNameValidator,
                    ]"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formModel.userEmail"
                    label="Merchant Email*"
                    type="email"
                    placeholder="<EMAIL>"
                    :rules="[requiredValidator, merchanEmailValidator]"
                  />
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}

:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

.v-row:last-child {
  margin-top: auto;
  padding: 16px;
  background: var(--v-theme-surface);
  position: sticky;
  bottom: 0;
  z-index: 1;
}
</style>
