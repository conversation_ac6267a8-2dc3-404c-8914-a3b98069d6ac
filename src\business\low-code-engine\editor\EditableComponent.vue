<template>
  <div v-if="isRoot" class="editable-component" :class="{ selected: isSelected }" :style="componentStyle"
    @click.stop="handleSelect">
    <RenderNode :nodes="[component]" :context="context" :selected-component-id="selectedComponentId"
      @component-click="handleNestedComponentClick" />
    <!-- 编辑操作按钮/边框等可在这里加 -->
  </div>
  <RenderNode v-else :nodes="[component]" :context="context" :selected-component-id="selectedComponentId"
    @component-click="handleNestedComponentClick" />
</template>

<script setup>
import { computed } from 'vue'
import RenderNode from '@/business/low-code-engine/editor/RenderNode.vue'

const props = defineProps({
  component: { type: Object, required: true },
  index: { type: Number, default: 0 },
  isSelected: { type: Boolean, default: false },
  isRoot: { type: Boolean, default: true },
  context: { type: Object, default: () => ({}) }, // 接收渲染上下文
  selectedComponentId: { type: String, default: null }, // 当前选中的组件ID
})

const emit = defineEmits(['select', 'update', 'delete', 'duplicate'])

function handleSelect() {
  console.log('🔥 EditableComponent: handleSelect被调用')
  console.log('🔥 EditableComponent: 完整组件对象:', props.component)
  console.log('🔥 EditableComponent: 组件信息:', {
    id: props.component.id,
    type: props.component.type,
    name: props.component.name,
    idType: typeof props.component.id
  })
  console.log('🔥 EditableComponent: 发出的ID:', props.component.id)
  emit('select', props.component.id)
}

function handleNestedComponentClick(componentId) {
  console.log('🔥 EditableComponent: 嵌套组件点击:', componentId)
  emit('select', componentId)
}

// 🔥 修复：直接使用传递过来的context，不再写死模拟数据

const componentStyle = computed(() => {
  // 🔥 修复：合并编辑器样式和组件样式
  const baseStyle = props.component._editableStyle || {}
  const componentStyle = props.component.style || {}

  const combinedStyle = {
    ...baseStyle,
    ...componentStyle
  }

  if (props.isSelected) {
    return {
      ...combinedStyle,
      border: '1.5px dashed #44D62C',
      boxShadow: '0 0 0 2px #44D62C33'
    }
  }

  return combinedStyle
})
</script>

<style scoped>
.editable-component {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: 1px dashed transparent;
}

.editable-component.selected {
  border: 1.5px dashed #44D62C;
  box-shadow: 0 0 0 2px #44D62C33;
}
</style>

<style>

.editable-component > div {
  padding-left: 8px;
  padding-right: 8px;
}
</style>