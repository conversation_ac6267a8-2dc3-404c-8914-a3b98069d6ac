import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// 加载插件
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * 日期时间处理工具类
 */
export const dateUtil = {
  /**
   * 格式化日期时间
   * @param {string|Date} date - 要格式化的日期
   * @param {string} format - 格式化模式，默认 'YYYY-MM-DD HH:mm:ss'
   * @param {string} tz - 目标时区，默认为本地时区
   * @returns {string} 格式化后的日期字符串
   */
  format(date, format = 'YYYY-MM-DD HH:mm:ss', tz) {
    if (!date) return ''
    tz = tz || useStorage('timezone', 'Asia/Shanghai').value
    if (tz) {
      if (tz === 'UTC') {
        return dayjs(date).utc().format(format)
      } else {
        return dayjs(date).tz(tz).format(format)
      }
    }
    return dayjs(date).format(format)
  },
  formatToUTC8(date) {
    if (!date) return null
    return this.format(date, 'YYYY-MM-DDTHH:mm:ss.SSS') + '+08:00'
  },
  /**
   * 检查日期是否有效
   * @param {string|Date} date - 要检查的日期
   * @returns {boolean} 是否为有效日期
   */
  isValid(date) {
    return dayjs(date).isValid()
  },

  /**
   * Get UTC offset of current timezone
   * @returns {number} Current timezone utc offset in hours
   */
  getLocalTimezoneUTCOffset() {
    return dayjs().utcOffset() / 60
  },

  /**
   * format given date to UTC time, only change utc offset
   * @param {string} date
   * @param {number} offset
   * @returns {string} formatted date string in 'YYYY-MM-DDTHH:mm:ss.SSSZ' format
   */
  formatWithUTCOffset(date, offset) {
    if (!date) return ''

    if(!offset || offset > 12 || offset < -12) {
      return 'no offset'
    }

    return dayjs(date).utcOffset(offset, true).format('YYYY-MM-DDTHH:mm:ss.SSSZ')
  },

  /**
   *
   * @param date {string} date
   * @param offset {number} utc offset
   * @returns {string} formatted date string in 'DD-MMM-YYYY HH:mm:ss (UTC+/-X)' format
   */
  formatWithUTCOffsetLabel(date, offset) {
    if (!date) return ''
    if (!offset || offset > 12 || offset < -12) return 'no offset'
    const minutes = offset * 60
    const d = dayjs(date).utcOffset(minutes)
    const timeStr = d.format('DD-MMM-YYYY HH:mm:ss')
    const sign = offset >= 0 ? '+' : '-'
    const absHour = Math.abs(offset)
    return `${timeStr} (UTC${sign}${absHour})`
  }
}

export default dateUtil
