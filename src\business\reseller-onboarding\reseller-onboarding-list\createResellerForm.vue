<script setup>
import {
  requiredValidator,
  emailValidator,
  urlValidator,
  lengthLimitationValidator,
  contactNumberValidator,
} from "@/utils/validators";

import { useLookupCodesInfo } from "@/business/reseller-onboarding/reseller-onboarding-list/useLookupCodesInfo.js";

import {
  VRadioGroup,
  VRadio,
} from "vuetify/components";

import AppAutocomplete from "@/components/AppAutocomplete.vue";

// form data
const formModel = defineModel({
  type: Object,
  required: true,
});

const props = defineProps({
  type: {
    type: String,
    required: true,
  },
});

const formRef = ref(null);
const countryList = useLookupCodesInfo("11").lookupCodeList;
const phoneCountryCodeList = useLookupCodesInfo("12").lookupCodeList;



// expose methods and data to parent component
defineExpose({
  form: formRef,
  validate: () => {
    return formRef.value.validate();
  },
  getParams: () => {
    const params = {
      resellerOnboardId: null,
      resellerType: formModel.value.resellerType.trim(),
      firstName: formModel.value.firstName.trim(),
      lastName: formModel.value.lastName.trim(),
      email: formModel.value.resellerEmail.trim(),
      phoneCountryCode: formModel.value.phoneCountryCode.trim(),
      phoneNo: formModel.value.phoneNo.trim(),
      regName: formModel.value.regName?.trim(),
      website: formModel.value.website?.trim(),
      regCountry: formModel.value.regCountry.trim(),
      mainDistributionCountry: formModel.value.mainDistributionCountry.trim(),
      otherDistributionCountry: isEmpty(formModel.value.otherDistributionCountry) ? null : formModel.value.otherDistributionCountry.join(","),
      expectedSalesPerMonth: formModel.value.expectedSalesPerMonth.trim(),
      requireKYBFlag: formModel.value.requireKybFlag.trim(),
      noRequireKYBReason: formModel.value.noRequireKybReason?.trim(),
      designation: null,
      regAnnualRevenue: null,
      typeOfPartner: null,
      distributionChannels: null,
      otherDistributionChannels: null,
      message: null,
      registrationSource: '3'

    };
    return params;
  },
  resetForm: () => {
    formModel.value = {
      resellerType: null,
      lastName: null,
      firstName: null,
      resellerEmail: null,
      phoneCountryCode: null,
      phoneNo: null,
      regName: null,
      website: null,
      regCountry: null,
      mainDistributionCountry: null,
      otherDistributionCountry: null,
      expectedSalesPerMonth: null,
      requireKybFlag: null,
      noRequireKybReason: null,
    };
  },
});
</script>

<template>
  <VCard ref="cardRef">
    <VCardItem class="pb-4 px-0">
      <VCardTitle> Create New Reseller Account </VCardTitle>
    </VCardItem>
    <VRow class="d-flex">
      <VCol cols="12" md="5" xl="5">
        <div class="form-wrapper">
          <VCardTitle> 1. Enter Reseller Account Info </VCardTitle>
          <VCardText>
            <VForm ref="formRef">
              <VRow>
                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="resellerType" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Reseller Type*" />
                    <VRadioGroup v-model="formModel.resellerType" inline :rules="[requiredValidator]">
                      <VRadio label="Individual" value="1"></VRadio>
                      <VRadio label="Company" value="2"></VRadio>
                    </VRadioGroup>
                  </div>
                </VCol>

                <VCol cols="6">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="firstName" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="First Name*" />
                    <AppTextField v-model="formModel.firstName" type="text" placeholder="Type reseller’s name"
                      :rules="[requiredValidator, (value) => lengthLimitationValidator(value, 100, 'First Name')]" />
                  </div>
                </VCol>

                <VCol cols="6">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="lastName" class="v-label mb-1 text-body-2 text-wrap leading-20px" text="Last Name*" />
                    <AppTextField v-model="formModel.lastName" type="text" placeholder="Type reseller’s name"
                      :rules="[requiredValidator, (value) => lengthLimitationValidator(value, 100, 'Last Name')]" />
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="lastName" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Reseller Email*" />
                    <AppTextField v-model="formModel.resellerEmail" type="email" placeholder="Type reseller’s email"
                      :rules="[requiredValidator, emailValidator]" />
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="contactNumber" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Contact Number*" />
                    <VRow no-gutters>
                      <VCol cols="12">
                        <AppTextField v-model="formModel.phoneNo" placeholder="Type in contact number" class="phoneNo"
                          :rules="[requiredValidator, contactNumberValidator, (value) => lengthLimitationValidator(value, 20, 'Contact Number')]"
                          type="text">
                          <template #prepend>
                            <AppAutocomplete v-model="formModel.phoneCountryCode" class="w-200px" placeholder="+00"
                              :items="phoneCountryCodeList" clearable clear-icon="tabler-x" filterable
                              :rules="[requiredValidator]" />
                          </template>
                        </AppTextField>
                      </VCol>
                    </VRow>
                  </div>
                </VCol>

                <VCol cols="12" v-if="formModel.resellerType === '2'">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="lastName" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Company Name*" />
                    <AppTextField v-model="formModel.regName" type="text" placeholder="Type reseller’s company name"
                      :rules="[requiredValidator, (value) => lengthLimitationValidator(value, 200, 'Company Name')]" />
                  </div>
                </VCol>
                <VCol cols="12" v-else>
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="lastName" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Company Name" />
                    <AppTextField v-model="formModel.regName" type="text" placeholder="Type reseller’s company name"
                      :rules="[(value) => lengthLimitationValidator(value, 200, 'Company Name')]" />
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="lastName" class="v-label mb-1 text-body-2 text-wrap leading-20px" text="Website" />
                    <AppTextField v-model="formModel.website" type="text" placeholder="Type reseller’s website"
                      :rules="[urlValidator, (value) => lengthLimitationValidator(value, 200, 'Website')]" />
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="regCountry" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Registered Country*" />
                    <VRow no-gutters>
                      <VCol cols="12">
                        <AppAutocomplete v-model="formModel.regCountry" placeholder="Value" :items="countryList"
                          clearable clear-icon="tabler-x" filterable :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="mainDistributionCountry" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Main Distribution Country*" />
                    <VRow no-gutters>
                      <VCol cols="12">
                        <AppAutocomplete v-model="formModel.mainDistributionCountry" placeholder="Value"
                          :items="countryList" clearable clear-icon="tabler-x" filterable
                          :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="otherDistributionCountry" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Other Distribution Country" />
                    <VRow no-gutters>
                      <VCol cols="12">
                        <AppAutocomplete v-model="formModel.otherDistributionCountry" placeholder="Value"
                          :items="countryList" clearable clear-icon="tabler-x" filterable multiple chips closable-chips
                          :rules="[requiredValidator]" />
                      </VCol>
                    </VRow>
                  </div>
                </VCol>

                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="expectedSalesPerMonth" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Expected Sales Volume Per Month*" />
                    <VRadioGroup v-model="formModel.expectedSalesPerMonth" :rules="[requiredValidator]">
                      <VRow>
                        <VCol cols="6">
                          <VRadio label="Below USD 20K" value="1" />
                          <VRadio label="USD 20K to USD 100K" value="2" />
                          <VRadio label="USD 100k to USD 200K" value="3" />
                        </VCol>
                        <VCol cols="6">
                          <VRadio label="USD 200K to USD 500K" value="4" />
                          <VRadio label="USD 500K to USD 1M" value="5" />
                          <VRadio label="Above USD 1M" value="6" />
                        </VCol>
                      </VRow>
                    </VRadioGroup>
                  </div>
                </VCol>
                <VDivider :thickness="3" />
                <VCol cols="12">
                  <div class="app-text-field flex-grow-1">
                    <VLabel for="requireKybFlag" class="v-label mb-1 text-body-2 text-wrap leading-20px"
                      text="Require KYB?*" />
                    <VRadioGroup v-model="formModel.requireKybFlag" :rules="[requiredValidator]">
                      <VRadio label="Yes" value="1" />
                      <VRadio label="No" value="0" />
                    </VRadioGroup>
                    <div v-if="formModel.requireKybFlag === '0'" style="padding-left: 24px;">
                      <VRadioGroup v-model="formModel.noRequireKybReason" :rules="[requiredValidator]">
                        <VRadio label="Internal Testing" value="1" />
                        <VRadio label="Self-Managed Channels" value="2" />
                      </VRadioGroup>
                    </div>
                  </div>
                </VCol>

              </VRow>
            </VForm>
          </VCardText>
        </div>
      </VCol>
    </VRow>
  </VCard>
</template>

<style lang="scss" scoped>
.form-wrapper {
  background: #1e1e1e;
  border-radius: 16px;
  height: 100%;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  width: 65vw;
}

:deep(.v-card-text) {
  padding-bottom: 0px !important;
}

:deep(.v-switch .v-label) {
  padding-inline-end: 10px;
  padding-inline-start: 0px;
}

:deep(.phoneNo > .v-input--horizontal) { 
  &>.v-input__prepend{
    margin-inline-end: 0px !important;
   .v-field {
      border-top-right-radius: 0px !important;
      border-bottom-right-radius: 0px !important;
    }
  }

  &>.v-input__control {
    .v-field {
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;
    }
  }


}
</style>
