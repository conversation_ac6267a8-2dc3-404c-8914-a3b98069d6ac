/**
 * 场景配置管理 Hook
 * 使用 IndexedDB 本地存储场景的图标和颜色配置
 */

import { onMounted, ref } from "vue";

import { useDB } from "./useDB.js";
import { useLowCodeAPI } from "@/business/low-code-engine/api/useLowCodeAPI";
import { useStorage } from "@/hooks/useStorage.js";

const dbScenarioConfigs = useDB().scenarioConfigs;



const { listAllScenarios, createScenario } = useLowCodeAPI();

export function useScenarioConfig() {
  const loading = ref(false);
  const error = ref(null);
  const scenarioConfigs = ref(new Map());
  const selectedScenario = ref(useStorage("selectedScenario", null));

  // 默认配置
  const defaultConfig = {
    icon: "mdi-puzzle-outline",
    color: "primary",
  };

  /**
   * 初始化 IndexedDB 并加载所有配置
   */
  const initializeConfigs = async () => {
    try {
      loading.value = true;
      error.value = null;

      // 并行获取后端数据和 IndexedDB 数据
      const [apiResponse, localConfigs] = await Promise.all([
        listAllScenarios(),
        dbScenarioConfigs.toArray(),
      ]);

      const apiScenarios = apiResponse.data || [];
      const mergedConfigs = new Map();

      // 首先，以后端数据为基础构建 Map
      apiScenarios.forEach((scenario) => {
        console.log(scenario, "scenario");
        if (scenario.scenario) {
          mergedConfigs.set(scenario.scenario, {
            id: scenario.id,
            scenario: scenario.scenario,
            description: scenario.description,
            fields: scenario.fields || [],
            indexes: scenario.indexes || [],
            status: scenario.active ? "Active" : "Inactive",
          });
        }
        console.log(mergedConfigs, "mergedConfigs");
      });

      // 然后，用 IndexedDB 的数据进行合并或覆盖
      localConfigs.forEach((localConfig) => {
        const existingConfig = mergedConfigs.get(localConfig.scenario);
        const configToStore = existingConfig
          ? { ...existingConfig, ...localConfig } // 合并，本地配置覆盖后端同名字段
          : { ...localConfig }; // 仅存在于本地的配置

        mergedConfigs.set(localConfig.scenario, configToStore);
      });

      scenarioConfigs.value = mergedConfigs;

      console.log(
        `Initialized ${mergedConfigs.size} scenario configs (merged from API and IndexedDB)`
      );
    } catch (err) {
      console.error("Failed to initialize scenario configs:", err);
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 保存场景配置到 IndexedDB
   * @param {Object} config - 配置对象
   */
  const saveScenarioConfig = async (config) => {
    try {
      loading.value = true;
      error.value = null;

      const existingConfig = scenarioConfigs.value.get(config.scenario);

      let savedConfig;
      if (existingConfig && existingConfig.id) {
        // 报错
        message.error("Scenario already exists");
      } else {
        // 新增配置
        const scenarioData = {
          scenario: config.scenario,
          description: config.description,
        };
        const newId = await dbScenarioConfigs.add({ ...scenarioData, icon: config.icon, color: config.color, scenarioId: config.scenarioId });
        console.log(newId, "newId");
        savedConfig = await dbScenarioConfigs.get(newId);

        // 更新后端数据
        const updateScenario = await createScenario(scenarioData);
        console.log(updateScenario, "updateScenario");
      }

      // 更新本地缓存
      scenarioConfigs.value.set(config.scenario, savedConfig);

      console.log("Scenario config saved successfully:", scenarioConfigs.value);
      return savedConfig;
    } catch (err) {
      console.error("Failed to save scenario config:", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取场景配置
   * @param {string} scenarioId - 场景ID
   * @returns {Object} 场景配置对象
   */
  const getScenarioConfig = (scenarioId) => {
    const config = scenarioConfigs.value.get(scenarioId);

    // 如果没有找到配置，返回默认配置
    if (!config) {
      return {
        scenarioId,
        ...defaultConfig,
        scenario: scenarioId,
        description: "",
      };
    }

    return config;
  };

  /**
   * 更新场景配置（仅更新部分字段）
   * @param {string} scenarioId - 场景ID
   * @param {Object} updates - 要更新的字段
   */
  const updateScenarioConfig = async (scenarioId, updates) => {
    try {
      const existingConfig = getScenarioConfig(scenarioId);
      const newConfig = {
        ...existingConfig,
        ...updates,
      };

      return await saveScenarioConfig(scenarioId, newConfig);
    } catch (err) {
      console.error("Failed to update scenario config:", err);
      throw err;
    }
  };

  /**
   * 获取场景的图标
   * @param {string} scenarioId - 场景ID
   * @returns {string} 图标名称
   */
  const getScenarioIcon = (scenarioId) => {
    return getScenarioConfig(scenarioId).icon;
  };

  /**
   * 获取场景的颜色
   * @param {string} scenarioId - 场景ID
   * @returns {string} 颜色名称
   */
  const getScenarioColor = (scenarioId) => {
    return getScenarioConfig(scenarioId).color;
  };

  const setCurrentScenario = (scenario) => {
    selectedScenario.value = scenario;
    useStorage("selectedScenario", scenario);
  };

  // 组件挂载时初始化
  onMounted(() => {
    initializeConfigs();
  });

  return {
    // 状态
    loading,
    error,
    scenarioConfigs,
    selectedScenario,

    // 方法
    initializeConfigs,
    setCurrentScenario,
    saveScenarioConfig,
    getScenarioConfig,
    updateScenarioConfig,
    getScenarioIcon,
    getScenarioColor,
  };
}
