import { onMounted, ref } from 'vue'

import isArray from 'lodash-es/isArray'
import merge from 'lodash-es/merge'
import mergeWith from 'lodash-es/mergeWith'
import { treeToList } from '@/utils/helpers'

export const usePermissionList = (roleId = '') => {
  const permissionList = ref([])
  const fetchPermissionList = async () => {
    const allRes = await $api('/api/admin-api/v1/role/all', {
      method: 'GET',
    })
    if (roleId) {
      const checkedRes = await $api(`/api/admin-api/v1/role/all?roleId=${roleId}`, {
        method: 'GET',
      })

      const checkedAccessList = getCheckedAccessList(checkedRes.data)

      replaceAccessList(allRes.data, checkedAccessList)
     
    } 
    permissionList.value = transformPermissionList(allRes.data)
  }

  onMounted(() => {
    fetchPermissionList()
  })

  return {
    permissionList,
  }
}

function getCheckedAccessList(checkedRes) {
  let result = []
  checkedRes?.forEach((item) => {
    result = result.concat(item.accessList)
    if (item.children?.length > 0) {
      item.children.forEach((child) => {
        result = result.concat(child.accessList)
      })
    }
  })
  return result
}

function replaceAccessList(allRes, checkedAccessList) {
  allRes.forEach((item) => {
    const accessList = item.accessList
    for (const access of accessList) {
      const target = checkedAccessList.find((c) => c.id === access.id)
      if (target) {
        access.markFlag = target.markFlag
      }
    }
    if (item.children.length > 0) {
      replaceAccessList(item.children, checkedAccessList)
    }
  })
}

function transformPermissionList(permissionList) {
  return permissionList.map((item) => {
    return {
      id: item.id,
      items: transformAccessList(item.children),
      name: item.name,
      isAdd: transformAccess(item, 'Add'),
      isViewed: transformAccess(item, 'View'),
      isEdit: transformAccess(item, 'Edit'),
      isDownload: transformAccess(item, 'Download'),
      isApprove: transformAccess(item, 'Approve'),
      isInitiate: transformAccess(item, 'Initiate'),
    }
  })

 
}

function transformAccessList(children) {
  if (children.length === 0) {
    return []
  }
  return children.map((item) => {
    const accessList = item.accessList
    return {
      id: item.id,
      name: item.name,
      isAdd: transformAccess(item, 'Add', accessList),
      isViewed: transformAccess(item, 'View', accessList),
      isEdit: transformAccess(item, 'Edit', accessList),
      isDownload: transformAccess(item, 'Download', accessList),
      isApprove: transformAccess(item, 'Approve', accessList),
      isInitiate: transformAccess(item, 'Initiate', accessList),
    }
  })
}

const accessActionMap = {
  View: 1,
  Add: 2,
  Edit: 3,
  Approve: 4,
  Initiate: 5,
  Download: 6,
}


function transformAccess(item, accessAction, accessList) {
  if (!accessList) { 
    accessList = []
    if (item.children.length > 0) {
      item.children.forEach((child) => {
        accessList = accessList.concat(child.accessList || [])
      })
    }
    // Check if any child elements have access in their accessList
    const hasAccess = accessList.some((access) => access.accessRightId === accessActionMap[accessAction]);
    
    // If no child elements have accessAction, set parent element to disabled
    const isDisabled = !hasAccess && item.children.length > 0;
  
    return {
      id: null,
      value: false,
      disabled: isDisabled, // Set disabled based on children's state
    }
  }


  const access = accessList.find((access) => access.accessRightId === accessActionMap[accessAction]);
  if (!access) {
    return {
      id: null,
      value: false,
      disabled: true,
    }
  }
  return {
    id: access.id,
    value: access.markFlag || false,
    disabled: false,
  }
}