<script setup>
import { useRouter } from "vue-router";
import CreateMerchantForm from "@/business/merchant-onboarding/merchant-onboarding-list/createMerchantForm.vue";
import { isEmpty } from '@/utils/helpers'
definePage({
  meta: {
    navActiveLink: "create-merchant",
    breadcrumb: "Create New Merchant Account",
  },
});

const router = useRouter();
const createMerchantFormRef = ref(null);
const isSaving = ref(false);
const PARSE_RESPONSE_ERROR = "Failed to parse error response";
const PENDING_ACTIVATE = 1;

// form data
const formModel = ref({
  userName: null,
  userEmail: null,
});

const handleCancel = () => {
  createMerchantFormRef.value?.resetForm();
  router.back();
};

const handleSave = async () => {
  if (isSaving.value) return;
  // validate form
  const { valid, errors } = await createMerchantFormRef.value?.validate();

  if (!valid) return;
  isSaving.value = true;
  try {
    const params = createMerchantFormRef.value?.getParams();

    const res = await $api("/api/merchant-onboard/v1/search", {
      method: "POST",
      data: {
        merchantOnboardIds: null,
        statusCodes: null,
        size: -1,
      },
    });

    const merchantNameExist = res?.data?.records?.find(item => item.name === params.name && item.status !== PENDING_ACTIVATE);
    const merchantEmailExist = res?.data?.records?.find(item => item.merchantEmail === params.email && item.status !== PENDING_ACTIVATE);

    if(!isEmpty(merchantNameExist) && !isEmpty(merchantEmailExist)){
      message.error("This merchant name and email is already registered");
      return;
    }

    if(!isEmpty(merchantNameExist)){
      message.error("This merchant name is already registered");
      return;
    }

    if(!isEmpty(merchantEmailExist)){
      message.error("This merchant email is already registered");
      return;
    }

    await $api("/api/merchant-onboard/v1/add", {
      method: "POST",
      data: params,
      showToast: false
    });
    message.success("Merchant created successfully");
    router.back();
  } catch (error) {
    console.error(error);
    const toastMsg =
      (error._data.message && error._data.message.includes(PARSE_RESPONSE_ERROR))
        ? "This merchant name or email is already registered"
        : "Failed to create new merchant";
    message.error(toastMsg);
  } finally {
    isSaving.value = false;
  }
};
</script>

<template>
  <VCard>
    <CreateMerchantForm ref="createMerchantFormRef" v-model="formModel" type="add" />

    <VRow class="d-flex mt-3">
      <VCol cols="5" class="d-flex justify-end">
        <VBtn variant="outlined" color="default" @click.stop="handleCancel">
          CANCEL
        </VBtn>
        <VBtn color="primary" class="ml-2" style="width: 150px" data-testid="save-button" @click.stop="handleSave">
          CREATE
        </VBtn>
      </VCol>
    </VRow>
  </VCard>
</template>
