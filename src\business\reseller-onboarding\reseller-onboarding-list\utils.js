export const resolveStatusVariant = (status, type = "color") => {
  const statusDefination = {
    '1': {
      color: "#00C853",
      text: "Enquiry Submitted",
      icon: "mdi-circle",
      size: "15px",
    },
    '2': {
      color: "#FD4949",
      text: "Enquiry Rejected",
      icon: "mdi-circle",
      size: "15px",
    },
    '3': {
      color: "#FFA800",
      text: "Pending Activate",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    '4': {
      color: "#FFA800",
      text: "Pending Onboard",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    '5': {
      color: "#FFA800",
      text: "Pending KYB",
      icon: "tabler-clock-hour-3-filled",
      size: "15px",
    },
    '7': {
      color: "#2962FF",
      text: "Processing KYB",
      icon: "mdi-circle-slice-4",
      size: "15px",
      iconStyle: { transform: "rotate(90deg)" },
    },
    '8': {
      color: "#d50000",
      text: "KYB Failed",
      icon: "mdi-alert-circle",
      size: "15px",
    },
    '6': {
      color: "#44d62c",
      text: "KYB Success",
      icon: "mdi-check-decagram",
      size: "15px",
    },
  };
  return statusDefination[status][type];
};

export const hasSomeElements = (str, elements) => {
  if (isEmpty(str)) return false;
  return elements.some((el) => str.includes(el));
};

export const hasActionPermission = (roleAccessList, groupId, roleId, rightId) => {
  return Boolean(
    roleAccessList
      ?.find(group => group.id === groupId)
      ?.roleList?.find(role => role.id === roleId)
      ?.accessList?.some(access => access.accessRightId === rightId)
  );
};

export const hasRole = (roleAccessList, groupId, roleIds) => {
  return Boolean(
    roleAccessList
      ?.find(group => group.id === groupId)
      ?.roleList?.find(role => roleIds.includes(role.id))
  );
};

// *********************************************** disable copy, right click and dev tools ***********************************************
export const disableCopy = (e) => {
  e.preventDefault();
  e.clipboardData.setData("text/plain", "");
};

export const disableDevTools = (e) => {
  if (
    e.key === "F12" ||
    (e.ctrlKey && e.shiftKey && e.key === "I") ||
    ((e.ctrlKey || e.metaKey) && e.key === "s")
  ) {
    e.preventDefault();
  }
};

export const disableRightClick = (e) => {
  e.preventDefault();
};

// *********************************************** handle parse tiff ***********************************************
export const parseTiffToImg = async (url) => {
  Tiff?.initialize({ TOTAL_MEMORY: 500 * 1024 * 1024 });

  const response = await fetch(url);
  const buffer = await response.arrayBuffer();
  const tiff = new Tiff({ buffer });
  const pages = [];
  for (let i = 0; i < tiff.countDirectory(); i++) {
    tiff.setDirectory(i);
    pages.push(tiff.toDataURL());
  }
  return pages;
}

// *********************************************** handle download ***********************************************
export const downloadByBlob = async (url, fileName) => {
  const response = await fetch(url);
  const blob = await response.blob();
  const objectURL = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = objectURL;
  link.download = fileName
  link.click();
  URL.revokeObjectURL(objectURL);
};